# Device ID Authentication System - Testing Guide

## Overview
This document outlines the testing procedures for the new Device ID-based authentication system that replaces the previous browser fingerprint system.

## System Features
1. **Device Identification**: Uses multiple device characteristics to create unique device signatures
2. **Single Session Enforcement**: Only one active session per student account
3. **Device Locking**: Students can only login from registered devices
4. **Admin Approval Workflow**: Admins can unbind devices to allow new device registration

## Pre-Testing Setup

### 1. Database Migration
Run the database migration to add new columns:
```sql
-- Execute the migration script
\i docs/database-migration-device-id.sql
```

### 2. Verify Database Schema
Check that new columns exist:
```sql
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'students' 
AND column_name IN ('approved_device_id', 'current_session_id', 'last_login_at', 'device_registered_at');
```

## Test Cases

### Test Case 1: Device ID Generation
**Objective**: Verify device ID generation works correctly

**Steps**:
1. Open browser developer console on `/student/login`
2. Execute: `await window.deviceIdentifier.generateDeviceId()`
3. Verify a hash string is returned
4. Execute again and verify same ID is returned
5. Check console for device characteristics: `window.deviceIdentifier.getCharacteristics()`

**Expected Results**:
- Device ID is a 64-character hexadecimal string
- Same device ID is returned on subsequent calls
- Device characteristics include screen, timezone, hardware info

### Test Case 2: First-Time Student Login
**Objective**: Test device registration on first login

**Steps**:
1. Create a new student account (if needed)
2. Admin approves the student account
3. Student logs in for the first time
4. Check database for device ID storage

**Expected Results**:
- Login succeeds
- `approved_device_id` column is populated
- `device_registered_at` timestamp is set
- `current_session_id` is set

**Database Verification**:
```sql
SELECT id, full_name, approved_device_id, current_session_id, device_registered_at, last_login_at
FROM students WHERE phone_number = 'test_phone_number';
```

### Test Case 3: Subsequent Login from Same Device
**Objective**: Verify login works from registered device

**Steps**:
1. Student logs out
2. Student logs in again from same device
3. Verify login succeeds

**Expected Results**:
- Login succeeds immediately
- `last_login_at` is updated
- `current_session_id` is updated

### Test Case 4: Login from Different Device (Should Fail)
**Objective**: Verify device locking prevents login from unregistered devices

**Steps**:
1. Open different browser or use incognito mode
2. Attempt to login with same student credentials
3. Verify login is blocked

**Expected Results**:
- Login fails with message about device registration
- Error message suggests contacting admin
- No session is created

### Test Case 5: Single Session Enforcement
**Objective**: Verify only one session can be active

**Steps**:
1. Student logs in from registered device (Browser A)
2. Verify session is active
3. Student logs in again from same device (Browser B)
4. Check that Browser A session is terminated

**Expected Results**:
- Browser B login succeeds
- Browser A session becomes invalid
- Only one `current_session_id` in database

**Verification**:
- Try accessing protected pages in Browser A (should redirect to login)
- Browser B should have full access

### Test Case 6: Admin Device Unbinding
**Objective**: Test admin can unbind devices

**Steps**:
1. Admin logs into admin panel
2. Navigate to student management
3. Find student with registered device
4. Click "Gỡ liên kết" (Unbind) button
5. Confirm the action

**Expected Results**:
- Device unbind succeeds
- Student's device fields are cleared in database
- Active session is terminated
- Student can register new device on next login

**Database Verification**:
```sql
SELECT approved_device_id, current_session_id, device_registered_at
FROM students WHERE id = 'student_id';
-- All should be NULL after unbinding
```

### Test Case 7: New Device Registration After Unbinding
**Objective**: Verify student can register new device after admin unbinding

**Steps**:
1. Admin unbinds student's device (Test Case 6)
2. Student attempts login from different device
3. Verify login succeeds and new device is registered

**Expected Results**:
- Login succeeds from new device
- New device ID is stored
- New registration timestamp is set

### Test Case 8: Admin Interface Display
**Objective**: Verify admin interface shows correct device information

**Steps**:
1. Admin views approved students list
2. Check device status column
3. Check session status column
4. Verify unbind buttons appear for students with devices

**Expected Results**:
- Device status shows "Device ID Registered" or "No Device"
- Session status shows "Active Session" or "No Active Session"
- Unbind buttons only appear for students with registered devices

### Test Case 9: Logout Functionality
**Objective**: Verify logout clears session properly

**Steps**:
1. Student logs in
2. Student clicks logout
3. Verify session is cleared

**Expected Results**:
- `current_session_id` is set to NULL in database
- Student is redirected to login page
- Accessing protected pages requires re-login

### Test Case 10: Error Handling
**Objective**: Test system behavior with various error conditions

**Steps**:
1. Disable JavaScript and attempt login
2. Block device ID generation and attempt login
3. Simulate database errors

**Expected Results**:
- Graceful error messages
- No system crashes
- Clear user guidance

## Performance Testing

### Device ID Generation Speed
- Measure time to generate device ID
- Should complete within 1-2 seconds
- Test on various devices/browsers

### Database Performance
- Monitor query performance with new columns
- Verify indexes are working
- Test with multiple concurrent logins

## Security Testing

### Device ID Spoofing
- Attempt to manually set device characteristics
- Verify system detects changes
- Test with browser extensions that modify fingerprints

### Session Security
- Verify session IDs are properly random
- Test session timeout behavior
- Verify old sessions are properly destroyed

## Browser Compatibility

Test on:
- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers (iOS Safari, Chrome Mobile)

## Rollback Plan

If issues are found:
1. Revert frontend to use `device_fingerprint` parameter
2. Update backend to prioritize `device_fingerprint` over `device_id`
3. Keep new columns for future migration

## Success Criteria

✅ All test cases pass
✅ No performance degradation
✅ Admin workflow functions correctly
✅ Error handling is robust
✅ Cross-browser compatibility confirmed
✅ Security requirements met

## Post-Deployment Monitoring

Monitor:
- Login success/failure rates
- Device ID generation errors
- Session management issues
- Admin unbind requests
- Database performance metrics
