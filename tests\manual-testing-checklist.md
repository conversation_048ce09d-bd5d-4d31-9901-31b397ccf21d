# Manual Testing Checklist - Document Upload Feature

## Pre-Testing Setup

### Environment Preparation
- [ ] Server is running on correct port
- [ ] Gemini API key is configured
- [ ] Admin authentication is working
- [ ] All dependencies are installed
- [ ] Browser developer tools are open

### Test Files Preparation
- [ ] Valid PDF file (< 10MB) with text content
- [ ] Valid DOCX file (< 10MB) with text content
- [ ] Large PDF file (> 10MB) for size testing
- [ ] Corrupted/invalid PDF file
- [ ] Corrupted/invalid DOCX file
- [ ] Non-document file (e.g., .txt, .jpg)

## Functional Testing

### 1. Modal Display Logic
#### Test Case: New Lesson Creation
- [ ] Navigate to `/admin/new`
- [ ] Verify modal appears automatically
- [ ] Check modal contains two options: "Tạo thủ công" and "Tải lên tài liệu"
- [ ] Verify modal styling and layout

#### Test Case: Edit Lesson
- [ ] Navigate to `/admin/edit/[lesson-id]`
- [ ] Verify modal does NOT appear
- [ ] Confirm normal editor functionality

### 2. Manual Creation Flow
- [ ] Click "Tạo thủ công" option
- [ ] Verify modal closes
- [ ] Confirm editor is accessible and functional
- [ ] Test normal lesson creation workflow

### 3. Document Upload Flow
#### Initial Upload Interface
- [ ] Click "Tải lên tài liệu" option
- [ ] Verify upload interface appears
- [ ] Check interface contains:
  - [ ] Upload dropzone
  - [ ] File browse button
  - [ ] Cancel and Process buttons
  - [ ] Close button (X)

#### File Selection - Browse Button
- [ ] Click "Chọn file từ máy tính"
- [ ] Verify file dialog opens
- [ ] Select valid PDF file
- [ ] Confirm file preview appears
- [ ] Check file name and size display correctly

#### File Selection - Drag & Drop
- [ ] Drag valid PDF file over dropzone
- [ ] Verify visual feedback (highlight effect)
- [ ] Drop file on dropzone
- [ ] Confirm file preview appears
- [ ] Test with DOCX file

#### File Validation
- [ ] Upload invalid file type (.txt)
- [ ] Verify error message: "Vui lòng chọn file PDF hoặc DOCX"
- [ ] Upload oversized file (> 10MB)
- [ ] Verify error message: "File quá lớn..."
- [ ] Test with corrupted PDF/DOCX files

#### File Preview and Management
- [ ] Verify file icon changes based on type (PDF vs DOCX)
- [ ] Check file name truncation for long names
- [ ] Test "Remove file" button functionality
- [ ] Confirm dropzone returns to initial state after removal

### 4. Document Processing
#### Successful Processing
- [ ] Upload valid PDF with text content
- [ ] Click "Xử lý tài liệu" button
- [ ] Verify processing status appears
- [ ] Check all processing steps activate in sequence:
  - [ ] "Tải lên file" - Active then Complete
  - [ ] "Trích xuất nội dung" - Active then Complete
  - [ ] "AI đang định dạng" - Active then Complete
  - [ ] "Hoàn tất" - Complete
- [ ] Confirm success message appears
- [ ] Verify upload interface closes automatically
- [ ] Check content appears in CodeMirror editor
- [ ] Verify preview panel updates with parsed questions

#### Error Handling
- [ ] Test with empty PDF file
- [ ] Test with image-only PDF (scanned document)
- [ ] Test with network disconnection during upload
- [ ] Verify appropriate error messages appear
- [ ] Check "Thử lại" button functionality

### 5. Editor Integration
#### Content Insertion
- [ ] Verify uploaded content appears in editor
- [ ] Check content formatting matches expected structure
- [ ] Confirm question numbering is correct
- [ ] Test multiple choice questions (A, B, C, D format)
- [ ] Test true/false questions with multiple statements
- [ ] Verify answer marking with asterisks (*)

#### Editor Functionality
- [ ] Test editor responsiveness after content insertion
- [ ] Verify syntax highlighting works
- [ ] Check preview panel updates correctly
- [ ] Test undo/redo functionality
- [ ] Confirm save functionality works

## User Interface Testing

### 6. Responsive Design
#### Desktop (1920x1080)
- [ ] Test modal layout and sizing
- [ ] Verify upload interface fits properly
- [ ] Check button spacing and alignment
- [ ] Test drag and drop area size

#### Tablet (768x1024)
- [ ] Verify modal adapts to screen size
- [ ] Check upload options stack properly
- [ ] Test touch interactions
- [ ] Confirm processing steps layout

#### Mobile (375x667)
- [ ] Test modal responsiveness
- [ ] Verify upload options become single column
- [ ] Check file selection on mobile
- [ ] Test processing steps on small screen
- [ ] Confirm buttons are touch-friendly

### 7. Cross-Browser Compatibility
#### Chrome
- [ ] Test all functionality
- [ ] Verify drag and drop works
- [ ] Check file upload performance

#### Firefox
- [ ] Test modal display
- [ ] Verify file processing
- [ ] Check editor integration

#### Safari (if available)
- [ ] Test iOS compatibility
- [ ] Verify touch interactions
- [ ] Check file handling

#### Edge
- [ ] Test Windows compatibility
- [ ] Verify all features work

## Performance Testing

### 8. Load Testing
- [ ] Upload maximum size file (10MB)
- [ ] Measure processing time
- [ ] Test with slow internet connection
- [ ] Verify timeout handling (30 seconds)

### 9. Memory Usage
- [ ] Monitor browser memory during upload
- [ ] Check for memory leaks after multiple uploads
- [ ] Test with multiple browser tabs open

## Security Testing

### 10. Authentication
- [ ] Test without admin login
- [ ] Verify unauthorized access is blocked
- [ ] Test with expired session

### 11. File Security
- [ ] Attempt to upload malicious files
- [ ] Test with files containing scripts
- [ ] Verify server-side validation

## Accessibility Testing

### 12. Keyboard Navigation
- [ ] Test tab navigation through modal
- [ ] Verify keyboard file selection
- [ ] Check focus indicators

### 13. Screen Reader Compatibility
- [ ] Test with screen reader software
- [ ] Verify alt text and labels
- [ ] Check ARIA attributes

## Error Recovery Testing

### 14. Network Issues
- [ ] Disconnect internet during upload
- [ ] Test with slow connection
- [ ] Verify retry functionality

### 15. Server Issues
- [ ] Test with server restart during processing
- [ ] Verify graceful error handling
- [ ] Check user feedback for server errors

## Integration Testing

### 16. End-to-End Workflow
- [ ] Complete workflow: Login → New Lesson → Upload → Edit → Save
- [ ] Test workflow with different file types
- [ ] Verify data persistence

### 17. Existing Feature Compatibility
- [ ] Confirm normal lesson creation still works
- [ ] Test lesson editing functionality
- [ ] Verify no conflicts with existing features

## Final Verification

### 18. Production Readiness
- [ ] All tests pass
- [ ] No console errors
- [ ] Performance is acceptable
- [ ] User experience is smooth
- [ ] Documentation is complete

## Test Results Summary

### Passed Tests
- [ ] Record number of passed tests
- [ ] Note any minor issues

### Failed Tests
- [ ] Document failed test cases
- [ ] Record error details
- [ ] Note required fixes

### Performance Metrics
- [ ] Average upload time: _____ seconds
- [ ] Average processing time: _____ seconds
- [ ] Memory usage: _____ MB
- [ ] File size limits tested: _____ MB

### Browser Compatibility
- [ ] Chrome: ✓/✗
- [ ] Firefox: ✓/✗
- [ ] Safari: ✓/✗
- [ ] Edge: ✓/✗

### Device Compatibility
- [ ] Desktop: ✓/✗
- [ ] Tablet: ✓/✗
- [ ] Mobile: ✓/✗

---

**Testing Date**: ___________
**Tester**: ___________
**Environment**: ___________
**Notes**: ___________
