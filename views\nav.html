<nav class="main-nav">
    <div class="nav-container">
        <a href="/" class="nav-logo">
            <span class="logo-gradient">Ôn luyện Vật lí</span>
        </a>
        <div class="nav-links">
            <a href="/lythuyet" class="nav-link">
                <i class="fas fa-book"></i>
                <span><PERSON><PERSON> thuyết</span>
            </a>
            <a href="/multiplechoice" class="nav-link">
                <i class="fas fa-tasks"></i>
                <span><PERSON><PERSON><PERSON><PERSON> tập</span>
            </a>
            <a href="/quizgame" class="nav-link">
                <i class="fas fa-trophy"></i>
                <span><PERSON>h phục</span>
            </a>
            <a href="/leaderboard" class="nav-link">
                <i class="fas fa-chart-line"></i>
                <span>Xếp hạng</span>
            </a>
            ${isAuthenticated ? `
                <a href="/profile" class="nav-link">
                    <i class="fas fa-user"></i>
                    <span><PERSON><PERSON> sơ</span>
                </a>
                <a href="/logout" class="nav-link">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Đăng xuất</span>
                </a>
            ` : `
                <a href="/student/login" class="nav-link">
                    <i class="fas fa-sign-in-alt"></i>
                    <span>Đăng nhập</span>
                </a>
                <a href="/student/register" class="nav-link">
                    <i class="fas fa-user-plus"></i>
                    <span>Đăng ký</span>
                </a>
            `}
        </div>
        <div class="nav-mobile-toggle">
            <i class="fas fa-bars"></i>
        </div>
    </div>
</nav>

<style>
    .main-nav {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        padding: 1rem 2rem;
        background: rgba(10, 10, 15, 0.8);
        backdrop-filter: blur(20px);
        border-bottom: 1px solid var(--glass-border);
        z-index: var(--z-sticky);
        transition: var(--transition-normal);
    }
    
    .nav-container {
        max-width: 1400px;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .nav-logo {
        font-size: 1.5rem;
        font-weight: 800;
        text-decoration: none;
        transition: var(--transition-fast);
    }
    
    .logo-gradient {
        background: var(--primary-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
    
    .nav-logo:hover {
        transform: scale(1.05);
    }
    
    .nav-links {
        display: flex;
        gap: 2rem;
        align-items: center;
    }
    
    .nav-link {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: var(--text-primary);
        text-decoration: none;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        border-radius: var(--radius-full);
        transition: var(--transition-fast);
        position: relative;
    }
    
    .nav-link:hover {
        background: var(--glass-bg);
        color: var(--neon-purple);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(168, 85, 247, 0.3);
    }
    
    .nav-link i {
        font-size: 1.1rem;
    }
    
    .nav-mobile-toggle {
        display: none;
        color: var(--text-primary);
        font-size: 1.5rem;
        cursor: pointer;
        transition: var(--transition-fast);
    }
    
    .nav-mobile-toggle:hover {
        color: var(--neon-purple);
    }
    
    /* Responsive */
    @media (max-width: 768px) {
        .nav-links {
            position: fixed;
            top: 70px;
            left: 0;
            right: 0;
            background: var(--bg-secondary);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--glass-border);
            flex-direction: column;
            padding: 1rem;
            gap: 0.5rem;
            transform: translateY(-100%);
            opacity: 0;
            transition: all var(--transition-normal);
            z-index: -1;
        }
        
        .nav-links.active {
            transform: translateY(0);
            opacity: 1;
            z-index: 999;
        }
        
        .nav-link {
            width: 100%;
            justify-content: center;
        }
        
        .nav-mobile-toggle {
            display: block;
        }
    }
    
    @media (max-width: 480px) {
        .main-nav {
            padding: 1rem;
        }
        
        .nav-logo {
            font-size: 1.2rem;
        }
        
        .nav-link span {
            display: none;
        }
        
        .nav-link {
            padding: 0.75rem;
        }
        
        .nav-links {
            flex-direction: row;
            flex-wrap: wrap;
            justify-content: center;
        }
    }
</style>

<script>
    // Mobile menu toggle
    document.addEventListener('DOMContentLoaded', () => {
        const mobileToggle = document.querySelector('.nav-mobile-toggle');
        const navLinks = document.querySelector('.nav-links');
        
        if (mobileToggle) {
            mobileToggle.addEventListener('click', () => {
                navLinks.classList.toggle('active');
                const icon = mobileToggle.querySelector('i');
                icon.classList.toggle('fa-bars');
                icon.classList.toggle('fa-times');
            });
            
            // Close menu when clicking outside
            document.addEventListener('click', (e) => {
                if (!e.target.closest('.main-nav')) {
                    navLinks.classList.remove('active');
                    const icon = mobileToggle.querySelector('i');
                    icon.classList.add('fa-bars');
                    icon.classList.remove('fa-times');
                }
            });
        }
    });
</script>