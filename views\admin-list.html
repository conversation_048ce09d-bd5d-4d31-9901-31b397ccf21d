<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>M<PERSON>n hình giáo viên - <PERSON><PERSON> s<PERSON>ch bài học</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Styles -->
    <link rel="stylesheet" href="/css/style.css">
    
    <!-- Canvas Animation -->
    <canvas id="network-canvas"></canvas>
</head>
<body>
    <!-- Loading Indicator -->
    <div id="loading-indicator" class="loading-indicator">
        <div class="spinner"></div>
        <p>Đang tải bài học...</p>
    </div>  

    <a href="/" class="home-button">
        <img src="https://styles.redditmedia.com/t5_851o4i/styles/profileIcon_0elfudeu2s5b1.jpg?width=256&height=256&frame=1&auto=webp&crop=256:256,smart&s=86be605407a08efe2894a6bacd089074aca51879" alt="Home">
    </a>
    
    <div class="admin-container">
        <h1>Màn hình giáo viên</h1>
        
        <div class="admin-controls">
            <a href="/admin/new" class="button primary">
                <i class="fas fa-plus-circle"></i>
                Tạo bài học mới
            </a>
            <a href="/history" class="button secondary">
                <i class="fas fa-history"></i>
                Lịch sử hoạt động
            </a>
            <a href="/admin/students" class="button">
                <i class="fas fa-users"></i>
                Quản lý học sinh
            </a>
            <button onclick="openReviewLessonModal()" class="button">
                <i class="fas fa-book-reader"></i>
                Tạo bài ôn tập
            </button>
        </div>
        
        <div class="admin-nav">
            <a href="/admin/quiz" class="nav-item">
                <i class="fas fa-gamepad"></i>
                <span>Trò chơi chinh phục</span>
            </a>
        </div>
        
        <div id="lesson-list">
            <h2>Bài học đã tạo</h2>
            <div class="lesson-items">
                <!-- Lessons will be dynamically added here -->
            </div>
        </div>
    </div>
    
    <!-- Review Lesson Modal -->
    <div id="review-lesson-modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); align-items: center; justify-content: center; z-index: 2000;">
        <div style="background: var(--bg-secondary); padding: 2.5rem; border-radius: var(--radius-xl); max-width: 500px; width: 90%; position: relative; border: 1px solid var(--glass-border); backdrop-filter: blur(20px); box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);">
            <span onclick="closeReviewLessonModal()" style="position: absolute; top: 1rem; right: 1rem; cursor: pointer; font-size: 1.5rem; color: var(--text-tertiary); transition: var(--transition-fast);">
                <i class="fas fa-times"></i>
            </span>
            <h2 style="margin-bottom: 2rem; text-align: center; background: var(--primary-gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
                <i class="fas fa-book-reader"></i> Tạo bài ôn tập
            </h2>
            <form id="review-lesson-form">
                <div class="form-group">
                    <label for="review-lesson-name">
                        <i class="fas fa-edit"></i> Tên bài
                    </label>
                    <input type="text" id="review-lesson-name" required placeholder="Nhập tên bài ôn tập..." class="modern-input" />
                </div>
                <div id="review-lesson-rows" style="margin-bottom: 1.5rem;"></div>
                <button type="button" onclick="addReviewRow()" class="button secondary" style="width: 100%; margin-bottom: 1rem;">
                    <i class="fas fa-plus"></i> Thêm bài
                </button>
                <button type="submit" class="button primary" style="width: 100%;">
                    <i class="fas fa-check"></i> Tạo bài ôn tập
                </button>
            </form>
        </div>
    </div>
    
    <!-- Background Animation Script -->
    <script src="/js/network-animation.js"></script>
    <script src="/js/admin-list.js"></script>
    <script src="/js/drag-utils.js"></script>
</body>
</html>