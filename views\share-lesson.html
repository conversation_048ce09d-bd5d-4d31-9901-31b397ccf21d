<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chia sẻ bài học: {{LESSON_NAME}}</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Styles -->
    <link rel="stylesheet" href="/css/style.css">
    
    <style>
        /* Share page specific styles */
        body {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }
        
        .share-container {
            position: relative;
            z-index: 1;
        }
        
        /* Loading overlay */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--bg-primary);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: var(--z-modal);
            transition: opacity 0.3s ease-out;
        }
        
        .loading-overlay.hidden {
            opacity: 0;
            pointer-events: none;
        }
        
        /* Mobile responsive */
        @media (max-width: 768px) {
            body {
                padding: 1rem;
            }
            
            .share-container {
                padding: 2rem;
            }
            
            .share-container h1 {
                font-size: 2rem;
            }
            
            .share-container .info {
                font-size: 1rem;
            }
            
            .share-container .start-button {
                font-size: 1.1rem;
                padding: 0.875rem 2rem;
            }
        }
        
        @media (max-width: 480px) {
            .share-container h1 {
                font-size: 1.75rem;
            }
            
            .share-container .info p {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }
            
            .share-container .info svg {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay">
        <div class="spinner"></div>
    </div>

    <!-- Background Animation -->
    <canvas id="network-canvas"></canvas>

    <!-- Home Button -->
    <a href="/" class="home-button">
        <img src="https://styles.redditmedia.com/t5_851o4i/styles/profileIcon_0elfudeu2s5b1.jpg?width=256&height=256&frame=1&auto=webp&crop=256:256,smart&s=86be605407a08efe2894a6bacd089074aca51879" alt="Home">
    </a>

    <div id="content-container" class="share-container">
        <img src="{{LESSON_IMAGE_URL}}" alt="Lesson Image" class="lesson-image" onerror="this.style.display='none';">
        <h1>{{LESSON_NAME}}</h1>
        <div class="info">
            <p>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <path d="M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.514 2 12 2zm0 18c-4.411 0-8-3.589-8-8s3.589-8 8-8 8 3.589 8 8-3.589 8-8 8z"></path>
                    <path d="M11 11h2v6h-2zm0-4h2v2h-2z"></path>
                </svg>
                <span>Số lượng câu hỏi: <strong>{{QUESTION_COUNT}}</strong></span>
            </p>
            <p>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <path d="M12 2a5 5 0 1 0 5 5 5 5 0 0 0-5-5zm0 8a3 3 0 1 1 3-3 3 3 0 0 1-3 3zm9 11v-1a7 7 0 0 0-7-7h-4a7 7 0 0 0-7 7v1h2v-1a5 5 0 0 1 5-5h4a5 5 0 0 1 5 5v1z"></path>
                </svg>
                <span>Tổng lượt đã làm: <strong>{{SUBMISSION_COUNT}}</strong></span>
            </p>
        </div>
        <a href="/lesson/{{LESSON_ID}}" class="start-button">
            <i class="fas fa-rocket"></i>
            Bắt đầu luyện tập
        </a>

        <!-- Placeholder for User History -->
        <div class="user-history-section">
            {{USER_HISTORY_HTML}}
        </div>
    </div>

    <script src="/js/network-animation.js"></script>
    <script>
        // Hide loader and show content when the page is fully loaded
        window.addEventListener('load', () => {
            const loader = document.getElementById('loading-overlay');
            const content = document.getElementById('content-container');
            
            if (loader) {
                loader.classList.add('hidden');
            }
            
            if (content) {
                content.classList.add('loaded');
            }

            // Apply specific classes to injected history
            const historySection = document.querySelector('.user-history-section');
            if (historySection && historySection.innerHTML.trim() !== '') {
                const historyCards = historySection.querySelectorAll('div');
                historyCards.forEach(card => card.classList.add('history-card'));
                
                const scoreLines = historySection.querySelectorAll('.history-card p:first-child');
                scoreLines.forEach(line => line.classList.add('score-line'));
                
                const links = historySection.querySelectorAll('.history-card a');
                links.forEach(link => {
                    link.classList.add('details-link');
                    // Add icon to links
                    if (!link.querySelector('i')) {
                        link.innerHTML = `<i class="fas fa-chart-line"></i> ${link.innerHTML}`;
                    }
                });
            }
        });
        
        // Add animation to elements
        document.addEventListener('DOMContentLoaded', () => {
            const elements = document.querySelectorAll('.share-container > *');
            elements.forEach((el, index) => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(20px)';
                el.style.transition = 'all 0.6s ease-out';
                
                setTimeout(() => {
                    el.style.opacity = '1';
                    el.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>