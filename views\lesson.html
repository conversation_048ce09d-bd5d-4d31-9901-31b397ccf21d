<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>B<PERSON>i học - Ôn luyện V<PERSON>t lí</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Styles -->
    <link rel="stylesheet" href="/css/style.css">
    <link rel="stylesheet" href="/css/lesson-questions.css">
    
    <!-- KaTeX -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.css">
    
    <style>
        /* Image zoom modal styles */
        .image-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.9);
            z-index: 9990;
            justify-content: center;
            align-items: center;
            opacity: 0;
            transition: opacity 0.3s ease;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }
        
        .image-modal.open {
            display: flex;
            opacity: 1;
        }
        
        .modal-image-container {
            position: relative;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }
        
        .modal-image {
            display: block;
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            transition: transform 0.1s ease;
            transform-origin: center;
            cursor: move;
        }
        
        .close-button {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 50px;
            height: 50px;
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            z-index: 9999;
            transition: var(--transition-normal);
            backdrop-filter: blur(10px);
        }
        
        .close-button:hover {
            background: var(--primary-gradient);
            transform: rotate(90deg);
            box-shadow: 0 0 20px rgba(168, 85, 247, 0.5);
        }
        
        .close-button:before, .close-button:after {
            content: '';
            position: absolute;
            width: 20px;
            height: 2px;
            background-color: white;
        }
        
        .close-button:before {
            transform: rotate(45deg);
        }
        
        .close-button:after {
            transform: rotate(-45deg);
        }
        
        .zoom-controls {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 10px;
            z-index: 9999;
        }
        
        .zoom-button {
            width: 50px;
            height: 50px;
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            color: white;
            border-radius: 50%;
            border: none;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            font-size: 24px;
            transition: var(--transition-normal);
            backdrop-filter: blur(10px);
        }
        
        .zoom-button:hover {
            background: var(--primary-gradient);
            transform: scale(1.1);
            box-shadow: 0 0 20px rgba(168, 85, 247, 0.5);
        }
        
        .reset-zoom-button {
            padding: 0 20px;
            height: 50px;
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            color: white;
            border-radius: var(--radius-full);
            border: none;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            font-size: 16px;
            transition: var(--transition-normal);
            backdrop-filter: blur(10px);
        }
        
        .reset-zoom-button:hover {
            background: var(--primary-gradient);
            transform: scale(1.05);
            box-shadow: 0 0 20px rgba(168, 85, 247, 0.5);
        }
        
        /* Additional modern styles */
        #submit-quiz-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.6s, height 0.6s;
        }
        
        #submit-quiz-btn:hover::before {
            width: 300px;
            height: 300px;
        }
        
        #submit-quiz-btn:active {
            transform: scale(0.98);
        }
        
        #result {
            margin-top: 2rem;
            padding: 2rem;
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            border-radius: var(--radius-xl);
            backdrop-filter: blur(20px);
            animation: fadeIn 0.6s ease-out;
        }
    </style>
</head>
<body>
    <!-- Loading Indicator -->
    <div id="loading-indicator" class="loading-indicator">
        <div class="spinner"></div>
        <p>Đang tải bài học...</p>
    </div>

    <!-- Background Animation -->
    <canvas id="network-canvas"></canvas>

    <!-- Home Button -->
    <a href="/" class="home-button">
        <img src="https://styles.redditmedia.com/t5_851o4i/styles/profileIcon_0elfudeu2s5b1.jpg?width=256&height=256&frame=1&auto=webp&crop=256:256,smart&s=86be605407a08efe2894a6bacd089074aca51879" alt="Home">
    </a>
    
    <!-- Image Zoom Modal -->
    <div id="image-modal" class="image-modal">
        <div class="modal-image-container">
            <img id="modal-image" class="modal-image" alt="Zoomed Image">
        </div>
        <div class="close-button" id="close-modal"></div>
        <div class="zoom-controls">
            <button class="zoom-button" id="zoom-out">−</button>
            <button class="reset-zoom-button" id="reset-zoom">Reset</button>
            <button class="zoom-button" id="zoom-in">+</button>
        </div>
    </div>
    
    <div class="lesson-container">
        <h1 id="lesson-title"></h1>
        
        <div id="lesson-image-container" class="lesson-image-container" style="display: none;">
            <img id="lesson-image" alt="Lesson Image" 
                 loading="eager" 
                 sizes="(max-width: 768px) 100vw, 800px"
                 onload="this.classList.add('loaded')">
        </div>
        
        <div id="abcd-questions">
            <h3><i class="fas fa-list-ol"></i> Câu hỏi trắc nghiệm</h3>
        </div>
        
        <div id="truefalse-questions">
            <h3><i class="fas fa-check-double"></i> Câu hỏi đúng/sai</h3>
        </div>
        
        <div id="number-questions">
            <h3><i class="fas fa-edit"></i> Câu hỏi trả lời ngắn</h3>
        </div>

        <button id="submit-quiz-btn" style="position: relative; overflow: hidden;">
            <i class="fas fa-paper-plane"></i> Nộp bài
        </button>
        <div id="result"></div>
    </div>
    
    <!-- Scripts -->
    <script src="/js/network-animation.js"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.js"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/contrib/auto-render.min.js"></script>
    <script src="/js/lesson.js"></script>
    
    <script>
        // Enhanced image zoom functionality
        document.addEventListener('DOMContentLoaded', function() {
            const lessonImage = document.getElementById('lesson-image');
            const questionImages = document.querySelectorAll('.question-image');
            const imageModal = document.getElementById('image-modal');
            const modalImage = document.getElementById('modal-image');
            const closeButton = document.getElementById('close-modal');
            const zoomInButton = document.getElementById('zoom-in');
            const zoomOutButton = document.getElementById('zoom-out');
            const resetZoomButton = document.getElementById('reset-zoom');
            
            let scale = 1;
            let panning = false;
            let pointX = 0;
            let pointY = 0;
            let startX = 0;
            let startY = 0;
            
            // Open modal functionality
            if (lessonImage) {
                lessonImage.addEventListener('click', function() {
                    openModal(this);
                });
            }
            
            questionImages.forEach(function(image) {
                image.addEventListener('click', function() {
                    openModal(this);
                });
            });
            
            document.addEventListener('click', function(e) {
                if (e.target && (e.target.id === 'lesson-image' || e.target.classList.contains('question-image'))) {
                    openModal(e.target);
                }
            });
            
            function openModal(imageElement) {
                modalImage.src = imageElement.src;
                modalImage.alt = imageElement.alt;
                imageModal.classList.add('open');
                document.body.style.overflow = 'hidden';
                resetZoom();
            }
            
            if (closeButton) {
                closeButton.addEventListener('click', closeModal);
            }
            
            imageModal.addEventListener('click', function(e) {
                if (e.target === imageModal) {
                    closeModal();
                }
            });
            
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && imageModal.classList.contains('open')) {
                    closeModal();
                }
            });
            
            function closeModal() {
                imageModal.classList.remove('open');
                document.body.style.overflow = '';
                resetZoom();
            }
            
            // Zoom controls
            zoomInButton.addEventListener('click', function() {
                setZoom(scale + 0.5);
            });
            
            zoomOutButton.addEventListener('click', function() {
                setZoom(Math.max(1, scale - 0.5));
            });
            
            resetZoomButton.addEventListener('click', resetZoom);
            
            function resetZoom() {
                scale = 1;
                pointX = 0;
                pointY = 0;
                modalImage.style.transform = `translate(${pointX}px, ${pointY}px) scale(${scale})`;
            }
            
            function setZoom(newScale) {
                scale = newScale;
                modalImage.style.transform = `translate(${pointX}px, ${pointY}px) scale(${scale})`;
            }
            
            // Mouse wheel zoom
            modalImage.addEventListener('wheel', function(e) {
                e.preventDefault();
                const xs = (e.clientX - pointX) / scale;
                const ys = (e.clientY - pointY) / scale;
                
                if (e.deltaY < 0) {
                    scale *= 1.1;
                } else {
                    scale /= 1.1;
                }
                
                scale = Math.max(1, scale);
                
                pointX = e.clientX - xs * scale;
                pointY = e.clientY - ys * scale;
                
                modalImage.style.transform = `translate(${pointX}px, ${pointY}px) scale(${scale})`;
            });
            
            // Drag to pan
            modalImage.addEventListener('mousedown', function(e) {
                e.preventDefault();
                
                if (scale > 1) {
                    panning = true;
                    startX = e.clientX - pointX;
                    startY = e.clientY - pointY;
                }
            });
            
            imageModal.addEventListener('mousemove', function(e) {
                e.preventDefault();
                
                if (panning && scale > 1) {
                    pointX = e.clientX - startX;
                    pointY = e.clientY - startY;
                    modalImage.style.transform = `translate(${pointX}px, ${pointY}px) scale(${scale})`;
                }
            });
            
            imageModal.addEventListener('mouseup', function(e) {
                panning = false;
            });
            
            imageModal.addEventListener('mouseleave', function(e) {
                panning = false;
            });
            
            // Mobile touch events
            let evCache = [];
            let prevDiff = -1;
            
            modalImage.addEventListener('touchstart', function(e) {
                if (e.touches.length > 1) {
                    e.preventDefault();
                }
                
                for (let i = 0; i < e.changedTouches.length; i++) {
                    evCache.push(e.changedTouches[i]);
                }
                
                if (e.touches.length === 1) {
                    panning = true;
                    startX = e.touches[0].clientX - pointX;
                    startY = e.touches[0].clientY - pointY;
                }
            });
            
            modalImage.addEventListener('touchmove', function(e) {
                if (e.touches.length > 1) {
                    e.preventDefault();
                    
                    const dist = Math.hypot(
                        e.touches[0].clientX - e.touches[1].clientX,
                        e.touches[0].clientY - e.touches[1].clientY
                    );
                    
                    if (prevDiff > 0) {
                        if (dist > prevDiff) {
                            scale *= 1.03;
                        } else if (dist < prevDiff) {
                            scale /= 1.03;
                        }
                        
                        scale = Math.max(1, scale);
                        
                        modalImage.style.transform = `translate(${pointX}px, ${pointY}px) scale(${scale})`;
                    }
                    
                    prevDiff = dist;
                } else if (e.touches.length === 1 && panning && scale > 1) {
                    pointX = e.touches[0].clientX - startX;
                    pointY = e.touches[0].clientY - startY;
                    modalImage.style.transform = `translate(${pointX}px, ${pointY}px) scale(${scale})`;
                }
            });
            
            modalImage.addEventListener('touchend', function(e) {
                for (let i = 0; i < e.changedTouches.length; i++) {
                    const idx = evCache.findIndex(cachedTouch => 
                        cachedTouch.identifier === e.changedTouches[i].identifier
                    );
                    if (idx >= 0) {
                        evCache.splice(idx, 1);
                    }
                }
                
                if (evCache.length < 2) {
                    prevDiff = -1;
                }
                
                if (e.touches.length === 0) {
                    panning = false;
                }
            });
            
            modalImage.addEventListener('touchmove', function(e) {
                if (e.touches.length > 1 || (scale > 1 && e.touches.length === 1)) {
                    e.preventDefault();
                }
            }, { passive: false });
        });
    </script>
</body>
</html>