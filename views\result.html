<!DOCTYPE html>
<html>
<head>
    <title><PERSON>em đ<PERSON>p <PERSON>n</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="format-detection" content="telephone=no">
    <link rel="stylesheet" href="/css/style.css">
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/highlightjs/cdn-release@11.9.0/build/styles/default.min.css">
    <script src="https://cdn.jsdelivr.net/gh/highlightjs/cdn-release@11.9.0/build/highlight.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- KaTeX CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.css" integrity="sha384-n8MVd4RsNIU0tAv4ct0nTaAbDJwPJzDEaqSD1tBCETKfPFPcxLR7diph9NkLkq3Y" crossorigin="anonymous">
    <!-- Custom styles for KaTeX -->
    <style>
        /* Hide the katex-html elements while keeping katex-mathml visible */
        .katex-html {
            display: none !important;
        }
        
        /* Image zoom modal styles */
        .image-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            z-index: 9990;
            justify-content: center;
            align-items: center;
            opacity: 0;
            transition: opacity 0.3s ease;
            overflow: hidden;
        }
        
        .image-modal.open {
            display: flex;
            opacity: 1;
        }
        
        .modal-image-container {
            position: relative;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }
        
        .modal-image {
            display: block;
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            transition: transform 0.1s ease;
            transform-origin: center;
            cursor: move;
        }
        
        .close-button {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 40px;
            height: 40px;
            background-color: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            z-index: 9999;
            transition: background-color 0.2s;
        }
        
        .close-button:hover {
            background-color: rgba(255, 255, 255, 0.5);
        }
        
        .close-button:before, .close-button:after {
            content: '';
            position: absolute;
            width: 20px;
            height: 2px;
            background-color: white;
        }
        
        .close-button:before {
            transform: rotate(45deg);
        }
        
        .close-button:after {
            transform: rotate(-45deg);
        }
        
        .zoom-controls {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 10px;
            z-index: 9999;
        }
        
        .zoom-button {
            width: 40px;
            height: 40px;
            background-color: rgba(255, 255, 255, 0.3);
            color: white;
            border-radius: 50%;
            border: none;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            font-size: 20px;
            transition: background-color 0.2s;
        }
        
        .zoom-button:hover {
            background-color: rgba(255, 255, 255, 0.5);
        }
        
        .reset-zoom-button {
            padding: 0 15px;
            height: 40px;
            background-color: rgba(255, 255, 255, 0.3);
            color: white;
            border-radius: 20px;
            border: none;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
        }
        
        .reset-zoom-button:hover {
            background-color: rgba(255, 255, 255, 0.5);
        }
        
        /* Make all images clickable */
        .question-image {
            cursor: pointer;
            transition: transform 0.2s;
            max-width: 100%;
            margin: 10px 0;
            border-radius: 8px;
        }
        
        .question-image:hover {
            transform: scale(1.02);
        }
        
        /* Question image container */
        .question-image-container {
            margin: 10px 0;
        }
        
        /* Rank styling and animations */
        #user-rank {
            transition: all 0.5s ease;
            min-height: 2.5em;
        }
        
        .rank-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 0.2em;
            opacity: 0;
            transform: scale(0.5);
            transition: all 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }
        
        .numeric-rank {
            font-size: 0.8em;
            font-weight: normal;
            opacity: 0.8;
        }
        
        .tier-rank {
            display: flex;
            flex-direction: column;
            align-items: center;
            font-weight: bold;
            text-shadow: 0 0 5px currentColor;
        }
        
        .tier-icon {
            font-size: 1.5em;
            margin-bottom: 0.2em;
            filter: drop-shadow(0 0 4px currentColor);
            animation: pulse 2s infinite;
        }
        
        .tier-name {
            font-size: 0.9em;
            white-space: nowrap;
        }
        
        /* Animation when rank is revealed */
        .rank-revealed .rank-container {
            opacity: 1;
            transform: scale(1);
        }
        
        /* Tier-specific animations */
        [data-tier="thách-đấu"] .tier-icon {
            animation: challenger-shine 3s infinite;
        }
        
        [data-tier="cao-thủ"] .tier-icon,
        [data-tier="tinh-anh"] .tier-icon,
        [data-tier="kim-cương"] .tier-icon {
            animation: shimmer 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        @keyframes shimmer {
            0% { filter: drop-shadow(0 0 3px currentColor); }
            50% { filter: drop-shadow(0 0 7px currentColor); }
            100% { filter: drop-shadow(0 0 3px currentColor); }
        }
        
        @keyframes challenger-shine {
            0% { 
                filter: drop-shadow(0 0 5px currentColor); 
                transform: scale(1) rotate(0deg);
            }
            25% { 
                filter: drop-shadow(0 0 10px currentColor);
                transform: scale(1.1) rotate(5deg);
            }
            50% { 
                filter: drop-shadow(0 0 15px currentColor);
                transform: scale(1.15) rotate(0deg);
            }
            75% { 
                filter: drop-shadow(0 0 10px currentColor);
                transform: scale(1.1) rotate(-5deg);
            }
            100% { 
                filter: drop-shadow(0 0 5px currentColor);
                transform: scale(1) rotate(0deg);
            }
        }
        
        /* Special visual effects for specific tiers */
        [data-tier="thách-đấu"] .stat-card {
            background: linear-gradient(135deg, #FF4EFF 0%, #b700a5 100%);
            animation: glow 3s infinite;
        }
        
        [data-tier="cao-thủ"] .stat-card {
            background: linear-gradient(135deg, #FF5555 0%, #b70000 100%);
        }
        
        [data-tier="tinh-anh"] .stat-card {
            background: linear-gradient(135deg, #8C00FF 0%, #5800a3 100%);
        }
        
        [data-tier="kim-cương"] .stat-card {
            background: linear-gradient(135deg, #00AAFF 0%, #0066cc 100%);
        }
        
        @keyframes glow {
            0% { box-shadow: 0 0 5px rgba(255, 78, 255, 0.5); }
            50% { box-shadow: 0 0 20px rgba(255, 78, 255, 0.8); }
            100% { box-shadow: 0 0 5px rgba(255, 78, 255, 0.5); }
        }
        
        /* Particle effects */
        .particles-container {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            pointer-events: none;
            z-index: 10;
        }
        
        .particle {
            position: absolute;
            top: 0;
            left: 0;
            border-radius: 50%;
            pointer-events: none;
        }
        
        /* Particle animations */
        @keyframes particleFade {
            0% { opacity: 1; }
            100% { opacity: 0; }
        }
        
        @keyframes particleMove0 {
            0% { transform: translate(0, 0); }
            100% { transform: translate(var(--x, 100px), calc(var(--y, 100px) - 50px)); }
        }
        
        @keyframes particleMove1 {
            0% { transform: translate(0, 0); }
            100% { transform: translate(calc(var(--x, -100px) * -1), calc(var(--y, 100px) - 70px)); }
        }
        
        @keyframes particleMove2 {
            0% { transform: translate(0, 0); }
            100% { transform: translate(calc(var(--x, 70px) - 30px), var(--y, -100px)); }
        }
        
        @keyframes particleMove3 {
            0% { transform: translate(0, 0); }
            100% { transform: translate(calc(var(--x, -70px) * -1), var(--y, -100px)); }
        }
        
        /* Special containers */
        .challenger-particles .particle {
            box-shadow: 0 0 10px currentColor;
        }
        
        /* Card styling enhancement */
        .stat-card.warning {
            position: relative;
            overflow: hidden;
        }
        
        /* Add a shine effect to top-tier cards */
        [data-tier="thách-đấu"] ~ .stat-card.warning::after,
        [data-tier="cao-thủ"] ~ .stat-card.warning::after,
        [data-tier="tinh-anh"] ~ .stat-card.warning::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(
                to right, 
                rgba(255, 255, 255, 0) 0%,
                rgba(255, 255, 255, 0.3) 50%,
                rgba(255, 255, 255, 0) 100%
            );
            transform: rotate(30deg);
            animation: shine 3s infinite;
        }
        
        @keyframes shine {
            0% { transform: translateX(-100%) rotate(30deg); }
            20% { transform: translateX(100%) rotate(30deg); }
            100% { transform: translateX(100%) rotate(30deg); }
        }
        
        /* Confetti animations */
        @keyframes confettiFall {
            0% { 
                transform: translateY(0) rotate(var(--rotation, 0deg)); 
                opacity: var(--opacity, 0.8);
            }
            70% {
                opacity: var(--opacity, 0.8);
            }
            100% { 
                transform: translateY(100vh) rotate(var(--rotation, 0deg));
                opacity: 0;
            }
        }
        
        @keyframes confettiRotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Add position relative to the container to allow for absolute positioning */
        .results-container {
            position: relative;
        }
        
        /* Make the stat card for ranking a little fancier */
        .stat-card.warning {
            transition: all 0.3s ease;
        }
        
        /* Add hover effects to the rank card */
        .stat-card.warning:hover {
            transform: scale(1.03);
            cursor: pointer;
        }
        
        /* Add badge-like appearance to higher ranks */
        [data-tier="thách-đấu"] .tier-name,
        [data-tier="cao-thủ"] .tier-name,
        [data-tier="tinh-anh"] .tier-name {
            position: relative;
            display: inline-block;
            background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 100%);
            padding: 2px 8px;
            border-radius: 12px;
            box-shadow: 0 0 10px currentColor;
        }

        /* Styles for Multiple Choice Options */
        .multiple-choice-options {
            margin-top: 15px;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .option-item {
            padding: 10px 15px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background-color: #ffffff; /* Default white background */
            color: #333; /* Default text color */
            transition: background-color 0.2s, border-color 0.2s, color 0.2s;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .mc-icon {
            font-size: 1.1em;
            min-width: 18px; /* Ensure space for icon */
            text-align: center;
        }

        /* Style for the CORRECT option */
        .option-item.correct-option {
            background-color: #e8f5e9; /* Light green background */
            border-color: #4CAF50;   /* Green border */
            color: #1b5e20;         /* Darker green text */
            font-weight: bold;
        }
        .option-item.correct-option .mc-icon {
            color: #4CAF50; /* Green check */
        }

        /* Style for the USER'S INCORRECT selection */
        .option-item.incorrect-selected {
            background-color: #ffebee; /* Light red background */
            border-color: #f44336;   /* Red border */
            color: #c62828;         /* Darker red text */
        }
        .option-item.incorrect-selected .mc-icon {
             color: #f44336; /* Red X */
        }

        /* Style for when the user selected the correct answer */
        /* Optional: Add a subtle indicator if needed, but green background might be enough */
        .option-item.user-selected.correct-option {
             /* box-shadow: inset 0 0 0 2px #4CAF50; */ /* Example: subtle green inset */
        }

        /* REMOVED the .user-selected generic style */
        /* REMOVED the CSS rule hiding .answer-section */
    </style>
</head>
<body>
    <!-- Loading Indicator -->
    <div id="loading-indicator" class="loading-indicator">
        <div class="spinner"></div>
        <p>Đang tải kết quả...</p>
    </div>

    <a href="/" class="home-button">
        <img src="https://styles.redditmedia.com/t5_851o4i/styles/profileIcon_0elfudeu2s5b1.jpg?width=256&height=256&frame=1&auto=webp&crop=256:256,smart&s=86be605407a08efe2894a6bacd089074aca51879" alt="Home">
    </a>

    <!-- Image Zoom Modal -->
    <div id="image-modal" class="image-modal">
        <div class="modal-image-container">
            <img id="modal-image" class="modal-image" alt="Zoomed Image">
        </div>
        <div class="close-button" id="close-modal"></div>
        <div class="zoom-controls">
            <button class="zoom-button" id="zoom-out">−</button>
            <button class="reset-zoom-button" id="reset-zoom">Reset</button>
            <button class="zoom-button" id="zoom-in">+</button>
        </div>
    </div>

    <div class="results-container">
        <div class="results-header">
            <h1>Xem đáp án</h1>
        </div>

        <div class="stats-grid">    
            <div class="stat-card primary">
                <div class="stat-icon">
                    <i class="fas fa-star"></i>
                </div>
                <div class="stat-value" id="score-value">-</div>
                <div class="stat-label">Điểm</div>
            </div>
            <div class="stat-card info">
                <div class="stat-icon">
                    <i class="fas fa-book"></i>
                </div>
                <div class="stat-value" id="lesson-name">-</div>
                <div class="stat-label">Bài học</div>
            </div>
            <div class="stat-card warning">
                <div class="stat-icon">
                    <i class="fas fa-trophy"></i>
                </div>
                <div class="stat-value" id="user-rank">-</div>
                <div class="stat-label">Xếp hạng của bạn</div>
            </div>
        </div>

        <div class="results-content">
            <div class="sort-buttons">
                <button onclick="sortResults('all')" class="sort-btn active">Tất cả</button>
                <button onclick="sortResults('correct')" class="sort-btn">Xem câu đúng</button>
                <button onclick="sortResults('incorrect')" class="sort-btn">Xem câu sai</button>
            </div>
            <div id="result" class="results-list"></div>
        </div>
    </div>
    <script src="/js/result.js"></script>
    <!-- KaTeX JS -->
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.js" integrity="sha384-XjKyOOlGwcjNTAIQHIpgOno0Hl1YQqzUOEleOLALmuqehneUG+vnGctmUb0ZY0l8" crossorigin="anonymous"></script>
    <!-- Optional: KaTeX auto-render extension -->
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/contrib/auto-render.min.js" integrity="sha384-+VBxd3r6XgURycqtZ117nYw44OOcIax56Z4dCRWbxyPt0Koah1uHoK0o4+/RRE05" crossorigin="anonymous"></script>
</body>
</html>