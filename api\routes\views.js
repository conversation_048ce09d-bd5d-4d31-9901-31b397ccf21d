const express = require('express');
const router = express.Router();
const path = require('path');

// Import middleware
const { 
  requireAdminAuth,
  requireStudentAuth,
  optionalAuth,
  addSessionInfo 
} = require('../middleware/auth');
const { longCacheMiddleware, noCacheMiddleware } = require('../middleware/cache');

// Helper function to serve HTML files
const serveHTML = (filename) => {
  return (req, res) => {
    res.sendFile(path.join(__dirname, '../../', filename));
  };
};

// Public pages
router.get('/',
  optionalAuth,
  addSessionInfo,
  longCacheMiddleware(3600), // 1 hour cache
  serveHTML('index.html')
);

router.get('/login',
  optionalAuth,
  addSessionInfo,
  noCacheMiddleware,
  serveHTML('login.html')
);

router.get('/register',
  optionalAuth,
  addSessionInfo,
  noCacheMiddleware,
  serveHTML('register.html')
);

router.get('/lessons',
  optionalAuth,
  addSessionInfo,
  longCacheMiddleware(1800), // 30 minutes cache
  serveHTML('lessons.html')
);

router.get('/lesson/:id',
  optionalAuth,
  addSessionInfo,
  longCacheMiddleware(1800), // 30 minutes cache
  serveHTML('lesson.html')
);

router.get('/leaderboard',
  optionalAuth,
  addSessionInfo,
  longCacheMiddleware(300), // 5 minutes cache
  serveHTML('leaderboard.html')
);

// Student-only pages
router.get('/student/dashboard',
  requireStudentAuth,
  addSessionInfo,
  noCacheMiddleware,
  serveHTML('student-dashboard.html')
);

router.get('/student/profile',
  requireStudentAuth,
  addSessionInfo,
  noCacheMiddleware,
  serveHTML('student-profile.html')
);

router.get('/student/results',
  requireStudentAuth,
  addSessionInfo,
  noCacheMiddleware,
  serveHTML('student-results.html')
);

router.get('/student/rating',
  requireStudentAuth,
  addSessionInfo,
  noCacheMiddleware,
  serveHTML('student-rating.html')
);

// Admin-only pages
router.get('/admin',
  requireAdminAuth,
  addSessionInfo,
  noCacheMiddleware,
  serveHTML('admin.html')
);

router.get('/admin/login',
  optionalAuth,
  addSessionInfo,
  noCacheMiddleware,
  serveHTML('admin-login.html')
);

router.get('/admin/lessons',
  requireAdminAuth,
  addSessionInfo,
  noCacheMiddleware,
  serveHTML('admin-lessons.html')
);

router.get('/admin/lessons/new',
  requireAdminAuth,
  addSessionInfo,
  noCacheMiddleware,
  serveHTML('admin-lesson-new.html')
);

router.get('/admin/lessons/:id/edit',
  requireAdminAuth,
  addSessionInfo,
  noCacheMiddleware,
  serveHTML('admin-lesson-edit.html')
);

router.get('/admin/students',
  requireAdminAuth,
  addSessionInfo,
  noCacheMiddleware,
  serveHTML('admin-students.html')
);

router.get('/admin/results',
  requireAdminAuth,
  addSessionInfo,
  noCacheMiddleware,
  serveHTML('admin-results.html')
);

router.get('/admin/ratings',
  requireAdminAuth,
  addSessionInfo,
  noCacheMiddleware,
  serveHTML('admin-ratings.html')
);

router.get('/admin/uploads',
  requireAdminAuth,
  addSessionInfo,
  noCacheMiddleware,
  serveHTML('admin-uploads.html')
);

router.get('/admin/statistics',
  requireAdminAuth,
  addSessionInfo,
  noCacheMiddleware,
  serveHTML('admin-statistics.html')
);

// Result viewing pages
router.get('/result/:id',
  optionalAuth,
  addSessionInfo,
  longCacheMiddleware(86400), // 24 hours cache
  serveHTML('result.html')
);

// Error pages
router.get('/404',
  optionalAuth,
  addSessionInfo,
  longCacheMiddleware(3600), // 1 hour cache
  serveHTML('404.html')
);

router.get('/500',
  optionalAuth,
  addSessionInfo,
  noCacheMiddleware,
  serveHTML('500.html')
);

// API documentation page
router.get('/docs',
  optionalAuth,
  addSessionInfo,
  longCacheMiddleware(3600), // 1 hour cache
  serveHTML('api-docs.html')
);

// Health check page
router.get('/health',
  longCacheMiddleware(60), // 1 minute cache
  (req, res) => {
    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage()
    });
  }
);

module.exports = router;
