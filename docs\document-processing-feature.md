# Document Processing Feature Documentation

## Overview

The Document Processing Feature allows educators to upload PDF and DOCX files that are automatically processed through AI (Gemini) to extract and format content into the system's lesson structure. This feature provides a seamless workflow for converting existing educational materials into the platform's standardized question format.

## Features

### Core Functionality
- **File Upload Support**: PDF and DOCX files up to 10MB
- **AI-Powered Processing**: Gemini AI extracts and formats content
- **Drag & Drop Interface**: Intuitive file selection
- **Real-time Processing Status**: Visual feedback during processing
- **Seamless Editor Integration**: Direct insertion into CodeMirror editor
- **Mobile Responsive**: Works on all device sizes

### Supported File Formats
- **PDF**: Text-based PDF files (not scanned images)
- **DOCX**: Microsoft Word documents

### Content Processing
- Extracts text content from uploaded documents
- Converts content to Vietnamese physics lesson format
- Creates multiple choice questions (ABCD format)
- Generates true/false questions with multiple statements
- Produces numerical answer questions
- Maintains proper question numbering and formatting

## User Workflow

### 1. Access Feature
- Navigate to `/admin/new` to create a new lesson
- <PERSON><PERSON> appears with two options:
  - **Manual Creation**: Traditional text editor
  - **Document Upload**: AI-powered file processing

### 2. Upload Process
1. **File Selection**:
   - Drag and drop file onto upload zone
   - Or click "Chọn file từ máy tính" to browse
   - File validation occurs immediately

2. **Processing Steps**:
   - **Upload**: File is sent to server
   - **Extract**: Text content is extracted from document
   - **AI Processing**: Gemini AI formats content
   - **Complete**: Content is inserted into editor

3. **Result**:
   - Formatted content appears in CodeMirror editor
   - Preview panel shows parsed questions
   - User can edit and refine content as needed

## Technical Implementation

### Backend Components

#### API Endpoint
- **Route**: `POST /api/admin/process-document`
- **Authentication**: Requires admin authentication
- **File Handling**: Uses multer middleware
- **Processing**: PDF-parse and mammoth libraries

#### AI Integration
- **Service**: Google Gemini 1.5 Flash
- **Prompt Engineering**: Vietnamese-specific formatting instructions
- **Error Handling**: Comprehensive error responses
- **Token Management**: Content truncation for large documents

### Frontend Components

#### Modal System
- **Initial Modal**: Choice between manual/upload
- **Upload Interface**: Drag & drop with file preview
- **Processing Status**: Real-time progress indicators

#### Editor Integration
- **Global Exposure**: `window.editor` for external access
- **Content Insertion**: Direct setValue with trigger events
- **Preview Update**: Automatic parsing and preview refresh

### File Structure
```
api/index.js                    # Backend endpoint and AI processing
views/admin-edit.html           # Modal HTML structure
public/js/document-upload.js    # Frontend upload logic
public/js/admin-stage1-editor.js # Editor integration
public/css/style.css            # Modal and upload styles
```

## Configuration

### Environment Variables
```bash
GEMINI_API_KEY=your_gemini_api_key_here
```

### File Upload Limits
- **Maximum Size**: 10MB per file
- **Allowed Types**: PDF, DOCX
- **Concurrent Uploads**: One at a time

### AI Processing Limits
- **Text Length**: 50,000 characters maximum
- **Token Limit**: ~12,500 tokens for Gemini
- **Timeout**: 30 seconds for AI processing

## Error Handling

### File Validation Errors
- **Invalid Type**: "Chỉ hỗ trợ file PDF và DOCX"
- **File Too Large**: "File quá lớn. Vui lòng chọn file nhỏ hơn 10MB"
- **Empty File**: "Không thể trích xuất nội dung từ file"

### Processing Errors
- **PDF Extraction**: "Không thể đọc file PDF. Vui lòng kiểm tra file không bị hỏng"
- **DOCX Extraction**: "Không thể đọc file DOCX. Vui lòng kiểm tra file không bị hỏng"
- **AI Processing**: "Không thể kết nối với AI để định dạng nội dung"
- **Editor Integration**: "Không thể tìm thấy trình soạn thảo"

### Network Errors
- **Connection Issues**: Automatic retry suggestions
- **Timeout Handling**: Clear error messages
- **Server Errors**: Detailed error logging

## Usage Guidelines

### Best Practices
1. **File Preparation**:
   - Ensure text is selectable in PDF files
   - Use clear question formatting in source documents
   - Avoid heavily formatted or complex layouts

2. **Content Quality**:
   - Review AI-generated content for accuracy
   - Verify question formatting and answer keys
   - Test questions before publishing

3. **Performance**:
   - Upload files during off-peak hours for faster processing
   - Keep file sizes reasonable for better performance
   - Use stable internet connection for uploads

### Limitations
- **Scanned PDFs**: Cannot extract text from image-based PDFs
- **Complex Formatting**: May lose complex layouts and formatting
- **Language**: Optimized for Vietnamese physics content
- **File Size**: 10MB limit may restrict very large documents

## Troubleshooting

### Common Issues

#### Upload Fails
1. Check file format (PDF/DOCX only)
2. Verify file size (under 10MB)
3. Ensure stable internet connection
4. Try refreshing page and uploading again

#### Processing Stuck
1. Wait for timeout (30 seconds)
2. Check browser console for errors
3. Verify Gemini API key configuration
4. Try with smaller file

#### Content Not Appearing
1. Check if editor is properly initialized
2. Verify JavaScript console for errors
3. Ensure admin authentication is valid
4. Try manual refresh of editor

#### Mobile Issues
1. Use landscape orientation for better experience
2. Ensure touch events are working
3. Check responsive design on different screen sizes

### Debug Information
- **Browser Console**: Check for JavaScript errors
- **Network Tab**: Monitor API requests and responses
- **Server Logs**: Review backend processing logs
- **File Validation**: Verify file type and size

## Security Considerations

### File Upload Security
- **Type Validation**: Server-side MIME type checking
- **Size Limits**: Prevents large file attacks
- **Authentication**: Admin-only access required
- **Sanitization**: Content is processed through AI before insertion

### API Security
- **Authentication**: JWT token validation
- **Rate Limiting**: Prevents abuse of AI API
- **Input Validation**: Comprehensive request validation
- **Error Handling**: No sensitive information in error messages

## Performance Optimization

### Frontend Optimizations
- **Lazy Loading**: Modal only loads when needed
- **Debounced Events**: Prevents excessive API calls
- **Progress Indicators**: Improves perceived performance
- **Responsive Design**: Optimized for all devices

### Backend Optimizations
- **Streaming**: Large file handling with streams
- **Caching**: AI responses could be cached for similar content
- **Compression**: File compression before processing
- **Async Processing**: Non-blocking file operations

## Future Enhancements

### Planned Features
- **Batch Upload**: Multiple file processing
- **Template Selection**: Different question formats
- **Content Preview**: Preview before insertion
- **History Tracking**: Upload and processing history

### Potential Improvements
- **OCR Support**: Scanned PDF processing
- **More File Types**: PowerPoint, Excel support
- **Advanced AI**: Better content understanding
- **Collaborative Editing**: Multi-user document processing

## Support

For technical support or feature requests, please contact the development team or create an issue in the project repository.

### Contact Information
- **Technical Issues**: Check server logs and browser console
- **Feature Requests**: Submit through project management system
- **Documentation Updates**: Contribute to this documentation

---

*Last Updated: 2025-07-01*
*Version: 1.0.0*
