<!DOCTYPE html>
<html>
<head>
    <title><PERSON><PERSON><PERSON> hình giáo viên - Chỉnh sửa nội dung bài học</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <link rel="stylesheet" href="/css/style.css">
    <!-- CodeMirror CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.14/codemirror.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.14/theme/default.css"> <!-- Using default theme -->
    <!-- KaTeX CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.css" integrity="sha384-n8MVd4RsNIU0tAv4ct0nTaAbDJwPJzDEaqSD1tBCETKfPFPcxLR7diph9NkLkq3Y" crossorigin="anonymous">
    <!-- Custom styles for KaTeX -->
    <style>
        /* Hide the katex-html elements while keeping katex-mathml visible */
        .katex-html {
            display: none !important;
        }
    </style>
    <!-- Keep theme CSS for now, JS change will apply default -->
</head>
<body>
    <a href="/" class="home-button">
        <img src="https://styles.redditmedia.com/t5_851o4i/styles/profileIcon_0elfudeu2s5b1.jpg?width=256&height=256&frame=1&auto=webp&crop=256:256,smart&s=86be605407a08efe2894a6bacd089074aca51879" alt="Home">
    </a>

    <!-- Document Upload Popup Overlay -->
    <div id="document-upload-popup" class="popup-overlay">
        <div class="popup-container">
            <div class="popup-header">
                <h2>🚀 Tạo bài học mới</h2>
                <button type="button" class="popup-close" onclick="closeUploadPopup()">&times;</button>
            </div>

            <div class="popup-content">
                <!-- Initial Choice Screen -->
                <div id="choice-screen" class="choice-screen">
                    <p class="choice-subtitle">Chọn cách bạn muốn tạo nội dung bài học</p>

                    <div class="choice-options">
                        <div class="choice-option" onclick="chooseManualCreation()">
                            <div class="choice-icon">✏️</div>
                            <h3>Tạo thủ công</h3>
                            <p>Nhập nội dung bài học trực tiếp</p>
                        </div>

                        <div class="choice-option" onclick="chooseDocumentUpload()">
                            <div class="choice-icon">📄</div>
                            <h3>Tải lên tài liệu</h3>
                            <p>AI tự động định dạng từ PDF/DOCX</p>
                        </div>
                    </div>
                </div>

                <!-- Document Upload Screen -->
                <div id="upload-screen" class="upload-screen" style="display: none;">
                    <div class="upload-header">
                        <button type="button" class="back-btn" onclick="showChoiceScreen()">← Quay lại</button>
                        <h3>📄 Tải lên tài liệu</h3>
                    </div>

                    <div id="upload-dropzone" class="upload-dropzone">
                        <div class="dropzone-content">
                            <div class="upload-icon">📁</div>
                            <p class="upload-text">Kéo thả file vào đây</p>
                            <div class="upload-divider">
                                <span>hoặc</span>
                            </div>
                            <button type="button" class="btn-browse" onclick="document.getElementById('file-input').click()">
                                📂 Chọn file từ máy tính
                            </button>
                            <input type="file" id="file-input" accept=".pdf,.docx" style="display: none;" onchange="handleFileSelect(event)">
                            <p class="upload-hint">Hỗ trợ PDF và DOCX • Tối đa 10MB</p>
                        </div>
                    </div>

                    <div id="file-preview" class="file-preview" style="display: none;">
                        <div class="file-card">
                            <div class="file-icon">📄</div>
                            <div class="file-details">
                                <div class="file-name"></div>
                                <div class="file-size"></div>
                            </div>
                            <button type="button" class="remove-file-btn" onclick="removeFile()" title="Xóa file">🗑️</button>
                        </div>
                    </div>

                    <div id="upload-error" class="upload-error" style="display: none;"></div>

                    <div id="processing-status" class="processing-status" style="display: none;">
                        <div class="processing-header">
                            <div class="processing-spinner">⚡</div>
                            <h3>Đang xử lý tài liệu...</h3>
                        </div>

                        <div class="processing-steps">
                            <div class="processing-step" data-step="upload">
                                <div class="step-icon">📤</div>
                                <span>Tải lên file</span>
                                <div class="step-status">⏳</div>
                            </div>
                            <div class="processing-step" data-step="extract">
                                <div class="step-icon">🔍</div>
                                <span>Trích xuất nội dung</span>
                                <div class="step-status">⏳</div>
                            </div>
                            <div class="processing-step" data-step="ai">
                                <div class="step-icon">🤖</div>
                                <span>AI đang định dạng</span>
                                <div class="step-status">⏳</div>
                            </div>
                            <div class="processing-step" data-step="complete">
                                <div class="step-icon">✅</div>
                                <span>Hoàn tất</span>
                                <div class="step-status">⏳</div>
                            </div>
                        </div>

                        <div class="processing-message"></div>
                    </div>

                    <div class="upload-actions">
                        <button type="button" class="btn-secondary" onclick="closeUploadPopup()">Hủy</button>
                        <button type="button" id="process-btn" class="btn-primary" onclick="processDocument()" disabled>
                            ⚡ Xử lý tài liệu
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
        </div>
    </div>

    <div class="lesson-editor stage-1">
        <div class="editor-header">
            <h1>Chỉnh sửa nội dung bài học</h1>
            <div class="header-actions">
                <button onclick="proceedToConfiguration()" class="next-btn">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M13 17l5-5-5-5M6 17l5-5-5-5"/></svg>
                    <span>Tiếp tục: Cấu hình</span>
                </button>
                <a href="/admin" class="back-btn">
                    <svg width="20" height="20" viewBox="0 0 24 24">
                        <path d="M19 12H5M12 19l-7-7 7-7"/>
                    </svg>
                    <span>Trở về danh sách</span>
                </a>
            </div>
        </div>

        <!-- Editor and Preview Section -->
        <div class="editor-preview-container">
            <!-- Swapped Order: Preview Panel First -->
            <div class="preview-panel">
                 <h2>Xem trước</h2>
                 <div id="realtime-preview" class="azota-preview">
                     <p>Xem trước sẽ xuất hiện ở đây...</p>
                 </div>
            </div>
            
            <!-- Swapped Order: Text Editor Panel Second -->
            <div class="text-editor-main-panel">
                <div class="editor-toolbar">
                     <h2>Câu hỏi (Nhập văn bản)</h2>
                     <div class="toolbar-buttons">
                         <button onclick="triggerImageUpload()" title="Tải ảnh lên"><i class="fas fa-upload"></i> Tải ảnh</button> 
                         <button onclick="addImageFromUrl()" title="Thêm ảnh từ URL"><i class="fas fa-link"></i> Ảnh URL</button>
                         <button onclick="insertLatexDelimiters('$$')" title="Thêm LaTeX ($$...$$)"><i class="fas fa-calculator"></i> LaTeX</button>
                         <button onclick="insertLatexDelimiters('$')" title="Thêm LaTeX ($...$)"><i class="fas fa-square-root-alt"></i> LaTeX</button>
                         <!-- Add other toolbar buttons if needed -->
                     </div>
                 </div>
                <textarea id="text-editor" spellcheck="false"></textarea>
                <!-- Add hidden file input for image uploads -->
                <input type="file" id="image-upload-input" accept="image/*" style="display: none;">
            </div>
        </div>
        
        <!-- Remove the old JSON editor panel -->
        <!-- Remove the old questions container and add button -->

    </div>
    
    <!-- CodeMirror JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.14/codemirror.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.14/mode/javascript/javascript.min.js"></script> 
    <!-- KaTeX JS -->
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.js" integrity="sha384-XjKyOOlGwcjNTAIQHIpgOno0Hl1YQqzUOEleOLALmuqehneUG+vnGctmUb0ZY0l8" crossorigin="anonymous"></script>
    <!-- Optional: KaTeX auto-render extension -->
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/contrib/auto-render.min.js" integrity="sha384-+VBxd3r6XgURycqtZ117nYw44OOcIax56Z4dCRWbxyPt0Koah1uHoK0o4+/RRE05" crossorigin="anonymous"></script> 
    
    <script src="/js/admin-stage1-editor.js"></script>
    <!-- Document upload functionality -->
    <script src="/js/document-upload.js"></script>
    <!-- Storage recovery script to help with session storage issues -->
    <script src="/js/storage-recovery.js"></script>
    <!-- Keep drag utils if needed for other parts, otherwise remove -->
    <!-- <script src="/js/drag-utils.js"></script> --> 
     <!-- Font Awesome for icons -->
     <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</body>
</html>