# Sample Test Content for Document Upload Feature

## Sample PDF Content for Testing

### Physics Lesson - Mechanics

**Chương 1: Độ<PERSON> học chất điểm**

Động học là phần của cơ học nghiên cứu chuyển động của các vật mà không quan tâm đến nguyên nhân gây ra chuyển động.

**1.1 Chuyển động thẳng đều**

Chuyển động thẳng đều là chuyển động có quỹ đạo là đường thẳng và vận tốc không đổi theo thời gian.

Phương trình chuyển động: x = x₀ + vt

Trong đó:
- x: tọa độ tại thời điểm t
- x₀: tọa độ ban đầu
- v: vận tốc (không đổi)
- t: thời gian

**Câu hỏi ôn tập:**

1. Một vật chuyển động thẳng đều với vận tốc 20 m/s. Quãng đường vật đi được trong 5 giây là:
A. 80 m
B. 100 m
C. 120 m
D. 150 m

2. Phương trình chuyển động thẳng đều có dạng:
A. x = x₀ + vt + ½at²
B. x = x₀ + vt
C. v = v₀ + at
D. s = vt

3. Trong chuyển động thẳng đều, đại lượng nào sau đây không đổi?
A. Quãng đường
B. Thời gian
C. Vận tốc
D. Tọa độ

**1.2 Chuyển động thẳng biến đổi đều**

Chuyển động thẳng biến đổi đều là chuyển động có quỹ đạo là đường thẳng và gia tốc không đổi.

Các công thức:
- v = v₀ + at
- x = x₀ + v₀t + ½at²
- v² = v₀² + 2a(x - x₀)

4. Một xe ô tô đang chạy với vận tốc 72 km/h thì hãm phanh chuyển động chậm dần đều và dừng lại sau 10 giây. Gia tốc của xe là:
A. -2 m/s²
B. -1.5 m/s²
C. -2.5 m/s²
D. -3 m/s²

5. Quãng đường xe đi được trong thời gian hãm phanh ở câu 4 là:
A. 80 m
B. 100 m
C. 120 m
D. 150 m

## Sample DOCX Content for Testing

### Bài tập Vật lý - Điện học

**Chương 2: Dòng điện không đổi**

**2.1 Định luật Ohm**

Định luật Ohm phát biểu: Cường độ dòng điện chạy qua một dây dẫn tỉ lệ thuận với hiệu điện thế đặt vào hai đầu dây dẫn và tỉ lệ nghịch với điện trở của dây dẫn.

Công thức: I = U/R

Trong đó:
- I: cường độ dòng điện (A)
- U: hiệu điện thế (V)
- R: điện trở (Ω)

**Bài tập:**

Câu 1: Một bóng đèn có điện trở 12Ω được mắc vào hiệu điện thế 6V. Cường độ dòng điện qua bóng đèn là:
A. 0.5 A
B. 1 A
C. 2 A
D. 3 A

Câu 2: Các phát biểu sau về định luật Ohm, phát biểu nào đúng?
a) Cường độ dòng điện tỉ lệ thuận với hiệu điện thế
b) Cường độ dòng điện tỉ lệ nghịch với điện trở
c) Định luật chỉ áp dụng cho dây dẫn kim loại
d) Điện trở phụ thuộc vào hiệu điện thế

Câu 3: Một điện trở 20Ω được mắc vào nguồn điện 12V. Công suất tiêu thụ của điện trở là:
A. 5.2 W
B. 6.4 W
C. 7.2 W
D. 8.1 W

**2.2 Định luật Joule-Lenz**

Nhiệt lượng tỏa ra trên một dây dẫn khi có dòng điện chạy qua được tính theo công thức:

Q = I²Rt = U²t/R = UIt

Câu 4: Một bếp điện có công suất 1000W hoạt động trong 2 giờ. Điện năng tiêu thụ là:
A. 1 kWh
B. 2 kWh
C. 3 kWh
D. 4 kWh

Câu 5: Nhiệt lượng tỏa ra trên một điện trở 10Ω khi có dòng điện 2A chạy qua trong 5 phút là:
A. 10000 J
B. 12000 J
C. 15000 J
D. 18000 J

## Expected AI Output Format

After processing the above content, the AI should format it as:

```
Câu 1: Một vật chuyển động thẳng đều với vận tốc 20 m/s. Quãng đường vật đi được trong 5 giây là:
A. 80 m
*B. 100 m
C. 120 m
D. 150 m

Câu 2: Phương trình chuyển động thẳng đều có dạng:
A. x = x₀ + vt + ½at²
*B. x = x₀ + vt
C. v = v₀ + at
D. s = vt

Câu 3: Trong chuyển động thẳng đều, đại lượng nào sau đây không đổi?
A. Quãng đường
B. Thời gian
*C. Vận tốc
D. Tọa độ

Câu 4: Một xe ô tô đang chạy với vận tốc 72 km/h thì hãm phanh chuyển động chậm dần đều và dừng lại sau 10 giây. Gia tốc của xe là:
*A. -2 m/s²
B. -1.5 m/s²
C. -2.5 m/s²
D. -3 m/s²

Câu 5: Quãng đường xe đi được trong thời gian hãm phanh ở câu 4 là:
A. 80 m
*B. 100 m
C. 120 m
D. 150 m
```

## Test Scenarios

### Scenario 1: Simple Physics Questions
- **Input**: Basic physics problems with clear questions
- **Expected**: Well-formatted multiple choice questions with correct answers marked
- **Validation**: Check question numbering, option formatting, answer marking

### Scenario 2: Mixed Question Types
- **Input**: Content with both multiple choice and true/false questions
- **Expected**: Proper formatting for different question types
- **Validation**: Verify different formatting patterns are applied correctly

### Scenario 3: Complex Content
- **Input**: Lengthy theoretical content without explicit questions
- **Expected**: AI-generated questions based on the content
- **Validation**: Questions should be relevant and properly formatted

### Scenario 4: Vietnamese Language Content
- **Input**: Content with Vietnamese physics terminology
- **Expected**: Proper handling of Vietnamese characters and terms
- **Validation**: No character encoding issues, proper Vietnamese formatting

### Scenario 5: Mathematical Formulas
- **Input**: Content with mathematical equations and symbols
- **Expected**: Formulas preserved or converted to readable format
- **Validation**: Mathematical notation is clear and correct

## Error Test Cases

### Invalid Content Tests
1. **Empty File**: File with no readable text
2. **Image-only PDF**: Scanned document without text layer
3. **Corrupted File**: Damaged or incomplete file
4. **Wrong Language**: Non-Vietnamese content
5. **Non-educational Content**: Irrelevant content type

### Large Content Tests
1. **Maximum Size**: 10MB file at size limit
2. **Very Long Text**: Content exceeding 50,000 characters
3. **Many Questions**: Document with 100+ questions
4. **Complex Formatting**: Heavily formatted document

### Network Error Tests
1. **Slow Connection**: Simulated slow upload
2. **Connection Drop**: Network interruption during processing
3. **Server Timeout**: Processing exceeding time limits
4. **API Failure**: Gemini API unavailable

## Performance Benchmarks

### Expected Performance Metrics
- **Small File (< 1MB)**: Processing time < 10 seconds
- **Medium File (1-5MB)**: Processing time < 20 seconds
- **Large File (5-10MB)**: Processing time < 30 seconds
- **Memory Usage**: < 100MB additional during processing
- **Success Rate**: > 95% for valid files

### Quality Metrics
- **Question Format Accuracy**: > 90% correctly formatted
- **Answer Marking Accuracy**: > 95% correct answer identification
- **Vietnamese Text Handling**: 100% character preservation
- **Content Relevance**: > 85% generated questions relevant to content

---

**Note**: These test cases should be used with actual PDF and DOCX files containing the above content to verify the complete upload and processing workflow.
