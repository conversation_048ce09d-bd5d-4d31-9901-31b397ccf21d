<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> sơ học viên - Ôn luyện Vật lí</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Styles -->
    <link rel="stylesheet" href="/css/style.css">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    
    <style>
        /* Additional styles for chart theming */
        .chart-container canvas {
            max-height: 350px;
        }
        
        /* Mobile responsive */
        @media (max-width: 768px) {
            .profile-header {
                flex-direction: column;
                text-align: center;
            }
            
            .profile-avatar {
                width: 80px;
                height: 80px;
                font-size: 2.5rem;
            }
            
            .profile-info h1 {
                font-size: 2rem;
            }
            
            .history-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }
            
            .history-change {
                align-self: flex-end;
            }
            
            .chart-container {
                height: 250px;
            }
            
            .rating-tiers {
                display: none;
            }
            
            .mobile-tier-container {
                display: block;
                overflow-x: auto;
                white-space: nowrap;
                padding-bottom: 1rem;
                margin-bottom: 1.5rem;
            }
            
            .mobile-tier-container .rating-tiers {
                display: inline-flex;
                flex-wrap: nowrap;
            }
        }
        
        @media (min-width: 769px) {
            .mobile-tier-container {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- Loading Indicator -->
    <div id="loading-indicator" class="loading-indicator">
        <div class="spinner"></div>
        <p>Đang tải hồ sơ...</p>
    </div>

    <!-- Background Animation -->
    <canvas id="network-canvas"></canvas>
    
    <!-- Home Button -->
    <a href="/" class="home-button">
        <img src="https://styles.redditmedia.com/t5_851o4i/styles/profileIcon_0elfudeu2s5b1.jpg?width=256&height=256&frame=1&auto=webp&crop=256:256,smart&s=86be605407a08efe2894a6bacd089074aca51879" alt="Home">
    </a>
    
    <div class="profile-container" id="profile-data">
        <!-- Profile content will be loaded here -->
    </div>

    <script src="/js/network-animation.js"></script>
    <script>
        // Define rating tiers
        const ratingTiers = [
            { name: "Newbie", min: 0, max: 1200, color: "#808080" },
            { name: "Học viên", min: 1200, max: 1400, color: "#43e97b" },
            { name: "Chuyên cần", min: 1400, max: 1600, color: "#3b82f6" },
            { name: "Giỏi", min: 1600, max: 1900, color: "#a855f7" },
            { name: "Xuất sắc", min: 1900, max: 2200, color: "#f59e0b" },
            { name: "Master", min: 2200, max: 3000, color: "#ef4444" }
        ];
        
        // Get rating tier color
        function getRatingColor(rating) {
            for (const tier of ratingTiers) {
                if (rating >= tier.min && rating <= tier.max) {
                    return tier.color;
                }
            }
            return "#808080";
        }
        
        // Get rating tier name
        function getRatingTier(rating) {
            for (const tier of ratingTiers) {
                if (rating >= tier.min && rating <= tier.max) {
                    return tier.name;
                }
            }
            return "Unrated";
        }
        
        async function loadProfile() {
            const profileContainer = document.getElementById('profile-data');
            const studentId = window.location.pathname.split('/').pop();
            const loadingIndicator = document.getElementById('loading-indicator');

            if (!studentId) {
                profileContainer.innerHTML = '<div class="error-message"><i class="fas fa-exclamation-circle"></i> Không tìm thấy ID học viên.</div>';
                loadingIndicator.style.display = 'none';
                return;
            }

            try {
                const response = await fetch(`/api/profile/${studentId}`);
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.error || `Lỗi ${response.status}`);
                }

                const data = await response.json();
                
                // Render Profile
                let profileHTML = `
                    <div class="profile-header">
                        <div class="profile-avatar">${data.student.full_name?.[0]?.toUpperCase() || '?'}</div>
                        <div class="profile-info">
                            <h1>${data.student.full_name || 'Vô danh'}</h1>
                            <p class="profile-rating">
                                <i class="fas fa-star"></i> ${Math.round(data.rating?.rating || 1500)} điểm
                                <span style="color: ${getRatingColor(data.rating?.rating || 1500)}; font-weight: 700;"> (${getRatingTier(data.rating?.rating || 1500)})</span>
                            </p>
                            <p><i class="fas fa-calendar-alt"></i> Tham gia: ${new Date(data.student.created_at).toLocaleDateString('vi-VN')}</p>
                        </div>
                    </div>
                `;
                
                // Add Rating Chart Section
                profileHTML += `
                    <div class="profile-section">
                        <h2><i class="fas fa-chart-line"></i> Biểu đồ điểm số</h2>
                        <div class="rating-tiers">
                            ${ratingTiers.map(tier => 
                                `<div class="rating-tier">
                                    <div class="tier-color" style="background-color: ${tier.color}"></div>
                                    <span>${tier.name} (${tier.min}-${tier.max})</span>
                                </div>`
                            ).join('')}
                        </div>
                        <div class="mobile-tier-container">
                            <div class="rating-tiers">
                                ${ratingTiers.map(tier => 
                                    `<div class="rating-tier">
                                        <div class="tier-color" style="background-color: ${tier.color}"></div>
                                        <span>${tier.name}</span>
                                    </div>`
                                ).join('')}
                            </div>
                        </div>
                `;
                
                if (data.ratingHistory && data.ratingHistory.length > 0) {
                    profileHTML += `<div class="chart-container"><canvas id="ratingChart"></canvas></div>`;
                } else {
                    profileHTML += `<div class="no-data">Chưa có dữ liệu điểm số để hiển thị biểu đồ.</div>`;
                }
                
                profileHTML += `</div>`;

                // Render Rating History
                if (data.ratingHistory && data.ratingHistory.length > 0) {
                    profileHTML += `
                        <div class="profile-section">
                            <h2><i class="fas fa-history"></i> Lịch sử thay đổi điểm</h2>
                            <ul class="rating-history-list">
                    `;
                    data.ratingHistory.forEach(item => {
                        let change = item.rating_change;
                        if (change === undefined || change === null) {
                            const prevRating = item.previous_rating || 0;
                            const newRating = item.new_rating || 0;
                            change = newRating - prevRating;
                        }
                        
                        const changeClass = change > 0 ? 'positive' : change < 0 ? 'negative' : '';
                        const changeSymbol = change > 0 ? '+' : '';
                        profileHTML += `
                            <li class="history-item">
                                <div class="history-details">
                                    <strong>${item.lesson_title || `Bài học ID: ${item.lesson_id}`}</strong> 
                                    <span><i class="fas fa-clock"></i> ${new Date(item.timestamp).toLocaleString('vi-VN')}</span>
                                </div>
                                <div class="history-change ${changeClass}">
                                    ${changeSymbol}${change}
                                    <i class="fas fa-arrow-${change > 0 ? 'up' : 'down'}"></i>
                                    (${Math.round(item.new_rating)})
                                </div>
                            </li>
                        `;
                    });
                    profileHTML += '</ul></div>';
                } else {
                    profileHTML += `
                        <div class="profile-section">
                            <h2><i class="fas fa-history"></i> Lịch sử thay đổi điểm</h2>
                            <div class="no-data">Chưa có lịch sử thay đổi điểm.</div>
                        </div>
                    `;
                }
                
                profileContainer.innerHTML = profileHTML;
                
                // Hide loading indicator
                loadingIndicator.style.display = 'none';
                
                // Create Rating Chart if data exists
                if (data.ratingHistory && data.ratingHistory.length > 0) {
                    createRatingChart(data.ratingHistory);
                }

            } catch (error) {
                console.error("Error loading profile:", error);
                profileContainer.innerHTML = `<div class="error-message"><i class="fas fa-exclamation-triangle"></i> Không thể tải hồ sơ: ${error.message}</div>`;
                loadingIndicator.style.display = 'none';
            }
        }
        
        function createRatingChart(historyData) {
            // Ensure history is sorted by timestamp
            historyData.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
            
            // Calculate rating changes if not provided
            historyData.forEach((item, index) => {
                if (item.rating_change === undefined || item.rating_change === null) {
                    if (index === 0) {
                        const startingRating = item.previous_rating || 1500;
                        item.rating_change = (item.new_rating || 0) - startingRating;
                    } else {
                        const prevRating = historyData[index - 1].new_rating || 0;
                        item.rating_change = (item.new_rating || 0) - prevRating;
                    }
                }
            });
            
            // Format data for chart
            const labels = historyData.map(item => {
                const date = new Date(item.timestamp);
                return date.toLocaleDateString('vi-VN', { day: 'numeric', month: 'numeric' });
            });
            
            const ratings = historyData.map(item => Math.round(item.new_rating || 1500));
            const pointColors = historyData.map(item => getRatingColor(item.new_rating || 1500));
            
            // Add starting point
            if (historyData.length > 0) {
                const firstEntry = historyData[0];
                const startingRating = firstEntry.previous_rating || 
                                      (Math.round(firstEntry.new_rating) - firstEntry.rating_change) || 
                                      1500;
                
                const firstDate = new Date(firstEntry.timestamp);
                firstDate.setDate(firstDate.getDate() - 1);
                
                labels.unshift(firstDate.toLocaleDateString('vi-VN', { day: 'numeric', month: 'numeric' }));
                ratings.unshift(startingRating);
                pointColors.unshift(getRatingColor(startingRating));
            }
            
            // Chart configuration
            const ctx = document.getElementById('ratingChart').getContext('2d');
            
            const chart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Điểm số',
                        data: ratings,
                        fill: false,
                        borderColor: '#a855f7',
                        tension: 0.1,
                        pointBackgroundColor: pointColors,
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 6,
                        pointHoverRadius: 8,
                        borderWidth: 3
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        tooltip: {
                            backgroundColor: 'rgba(10, 10, 15, 0.9)',
                            borderColor: 'rgba(168, 85, 247, 0.5)',
                            borderWidth: 1,
                            titleColor: '#fff',
                            bodyColor: '#b8bcc8',
                            padding: 12,
                            displayColors: false,
                            callbacks: {
                                title: function(tooltipItems) {
                                    const idx = tooltipItems[0].dataIndex;
                                    if (idx === 0 && idx < historyData.length) {
                                        return 'Điểm ban đầu';
                                    } else {
                                        const historyIdx = idx - 1 >= 0 ? idx - 1 : 0;
                                        const item = historyData[historyIdx];
                                        return item ? (item.lesson_title || `Bài học ID: ${item.lesson_id}`) : '';
                                    }
                                },
                                label: function(context) {
                                    const rating = context.raw;
                                    return `Điểm: ${rating} (${getRatingTier(rating)})`;
                                },
                                afterLabel: function(context) {
                                    const idx = context.dataIndex;
                                    if (idx > 0 && idx - 1 < historyData.length) {
                                        const change = historyData[idx - 1].rating_change;
                                        if (change && change !== 0) {
                                            const sign = change > 0 ? '+' : '';
                                            return `Thay đổi: ${sign}${change}`;
                                        }
                                    }
                                    return '';
                                }
                            }
                        },
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            title: {
                                display: true,
                                text: 'Điểm số',
                                color: '#b8bcc8'
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.05)'
                            },
                            ticks: {
                                color: '#b8bcc8'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            },
                            ticks: {
                                color: '#b8bcc8',
                                maxRotation: 45,
                                minRotation: 45
                            }
                        }
                    }
                }
            });
        }

        document.addEventListener('DOMContentLoaded', loadProfile);
    </script>
</body>
</html>