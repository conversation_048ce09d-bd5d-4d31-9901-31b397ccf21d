<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bảng xếp hạng - Ôn luyện Vật lí</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Styles -->
    <link rel="stylesheet" href="/css/style.css">
    
    <style>
        /* Mobile responsive adjustments */
        @media (max-width: 768px) {
            .leaderboard-container {
                padding: 1rem;
                margin-top: 5rem;
            }
            
            .leaderboard-header h1 {
                font-size: 2rem;
            }
            
            .leaderboard-header p {
                font-size: 1rem;
            }
            
            .filter-button {
                padding: 0.5rem 1.5rem;
                font-size: 0.9rem;
            }
            
            .leaderboard-table th,
            .leaderboard-table td {
                padding: 1rem;
                font-size: 0.9rem;
            }
            
            .user-avatar {
                width: 35px;
                height: 35px;
                font-size: 1rem;
            }
            
            /* Add scroll hint */
            .table-responsive-wrapper::after {
                content: '';
                position: absolute;
                top: 0;
                right: 0;
                bottom: 0;
                width: 30px;
                background: linear-gradient(to right, transparent, rgba(10, 10, 15, 0.8));
                pointer-events: none;
                opacity: 0.8;
            }
        }
        
        @media (max-width: 480px) {
            .leaderboard-header h1 {
                font-size: 1.8rem;
            }
            
            .filter-button {
                padding: 0.5rem 1rem;
                font-size: 0.85rem;
            }
            
            .rank {
                font-size: 1rem;
            }
            
            .medal {
                font-size: 1.2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Loading Indicator -->
    <div id="loading-indicator" class="loading-indicator">
        <div class="spinner"></div>
        <p>Đang tải bảng xếp hạng...</p>
    </div>

    <!-- Background Animation -->
    <canvas id="network-canvas"></canvas>
    
    <!-- Home Button -->
    <a href="/" class="home-button">
        <img src="https://styles.redditmedia.com/t5_851o4i/styles/profileIcon_0elfudeu2s5b1.jpg?width=256&height=256&frame=1&auto=webp&crop=256:256,smart&s=86be605407a08efe2894a6bacd089074aca51879" alt="Home">
    </a>
    
    <div class="leaderboard-container">
        <div class="leaderboard-header">
            <h1>Bảng xếp hạng</h1>
            <p>Xem thứ hạng của bạn so với các học viên khác 🏆</p>
        </div>

        <div class="leaderboard-filters">
            <button class="filter-button active" data-filter="all">
                <i class="fas fa-globe"></i> Tất cả
            </button>
            <button class="filter-button" data-filter="month">
                <i class="fas fa-calendar-alt"></i> Tháng này
            </button>
            <button class="filter-button" data-filter="week">
                <i class="fas fa-calendar-week"></i> Tuần này
            </button>
        </div>

        <div class="table-responsive-wrapper">
            <div id="leaderboard-content">
                <!-- Table will be inserted here by JS -->
            </div>
        </div>

        <div class="pagination">
            <button id="prev-page" disabled>
                <i class="fas fa-chevron-left"></i> Trước
            </button>
            <span id="page-info">Trang 1</span>
            <button id="next-page">
                Tiếp <i class="fas fa-chevron-right"></i>
            </button>
        </div>
    </div>

    <script src="/js/network-animation.js"></script>
    <script>
        let currentPage = 1;
        let currentFilter = 'all';
        const pageSize = 20;
        const loadingIndicator = document.getElementById('loading-indicator');

        async function loadLeaderboard() {
            const contentDiv = document.getElementById('leaderboard-content');
            contentDiv.innerHTML = '<div class="loading">Đang tải...</div>';
            
            // Disable pagination during load
            document.getElementById('prev-page').disabled = true;
            document.getElementById('next-page').disabled = true;

            try {
                const response = await fetch(`/api/ratings?page=${currentPage}&filter=${currentFilter}`);
                const ratings = await response.json();

                if (!response.ok) {
                    const errorData = ratings; 
                    console.error('API Error fetching ratings:', errorData);
                    contentDiv.innerHTML = `<div class="error-message"><i class="fas fa-exclamation-circle"></i> Lỗi tải bảng xếp hạng: ${errorData.details || errorData.error || 'Unknown error'}</div>`;
                    loadingIndicator.style.display = 'none';
                    return;
                }
                
                if (!ratings || ratings.length === 0) {
                    contentDiv.innerHTML = '<div class="no-data"><i class="fas fa-trophy"></i><br>Không có dữ liệu xếp hạng cho bộ lọc này.</div>';
                    document.getElementById('page-info').textContent = `Trang ${currentPage}`;
                    document.getElementById('prev-page').disabled = currentPage === 1;
                    document.getElementById('next-page').disabled = true;
                    loadingIndicator.style.display = 'none';
                    return;
                }

                const table = document.createElement('table');
                table.className = 'leaderboard-table';
                
                const thead = document.createElement('thead');
                thead.innerHTML = `
                    <tr>
                        <th class="center-align rank-column">Hạng</th>
                        <th class="name-column">Học sinh</th>
                        <th class="center-align score-column">Điểm</th>
                        <th class="center-align change-column">Thay đổi</th> 
                    </tr>
                `;
                table.appendChild(thead);

                const tbody = document.createElement('tbody');
                ratings.forEach((rating, index) => {
                    const rank = (currentPage - 1) * pageSize + index + 1;
                    const row = document.createElement('tr');
                    if (rank <= 3) row.className = 'top-3';

                    const ratingChange = 0; // TODO: Implement rating change calculation
                    const changeClass = ratingChange > 0 ? 'positive' : ratingChange < 0 ? 'negative' : '';
                    const changeSymbol = ratingChange > 0 ? '+' : '';
                    const changeIcon = ratingChange > 0 ? 'arrow-up' : ratingChange < 0 ? 'arrow-down' : 'minus';
                    
                    row.innerHTML = `
                        <td class="rank center-align">
                            ${rank <= 3 ? `<span class="medal ${['gold', 'silver', 'bronze'][rank-1]}"><i class="fas fa-medal"></i></span>` : ''}
                            ${rank}
                        </td>
                        <td class="name-cell">
                            <div class="user-profile">
                                <div class="user-avatar">${rating.students?.full_name?.[0]?.toUpperCase() || '?'}</div>
                                <a href="/profile/${rating.student_id || '#'}" title="Xem hồ sơ của ${rating.students?.full_name || 'Vô danh'}">
                                    <span>${rating.students?.full_name || 'Vô danh'}</span>
                                </a>
                            </div>
                        </td>
                        <td class="center-align rating-value">${Math.round(rating.rating)}</td>
                        <td class="center-align">
                            <span class="rating-change ${changeClass}">
                                ${changeSymbol}${Math.abs(ratingChange)}
                                <i class="fas fa-${changeIcon}"></i>
                            </span>
                        </td>
                    `;
                    tbody.appendChild(row);
                });
                table.appendChild(tbody);

                contentDiv.innerHTML = '';
                contentDiv.appendChild(table);

                // Update pagination 
                document.getElementById('prev-page').disabled = currentPage === 1;
                document.getElementById('page-info').textContent = `Trang ${currentPage}`;
                document.getElementById('next-page').disabled = ratings.length < pageSize;
                
                // Hide loading indicator
                loadingIndicator.style.display = 'none';

            } catch (error) {
                console.error('Error loading leaderboard:', error);
                contentDiv.innerHTML = '<div class="error-message"><i class="fas fa-exclamation-triangle"></i> Không thể tải bảng xếp hạng. Vui lòng thử lại sau.</div>';
                document.getElementById('prev-page').disabled = currentPage === 1;
                document.getElementById('next-page').disabled = true;
                document.getElementById('page-info').textContent = `Trang ${currentPage}`;
                loadingIndicator.style.display = 'none';
            }
        }

        // Event Listeners
        document.querySelectorAll('.filter-button').forEach(button => {
            button.addEventListener('click', () => {
                if (button.classList.contains('active')) return;
                
                document.querySelectorAll('.filter-button').forEach(b => b.classList.remove('active'));
                button.classList.add('active');
                currentFilter = button.dataset.filter;
                currentPage = 1;
                loadLeaderboard();
            });
        });

        document.getElementById('prev-page').addEventListener('click', () => {
            if (currentPage > 1) {
                currentPage--;
                loadLeaderboard();
            }
        });

        document.getElementById('next-page').addEventListener('click', () => {
            if (!document.getElementById('next-page').disabled) {
                currentPage++;
                loadLeaderboard();
            }
        });

        // Initial load
        document.addEventListener('DOMContentLoaded', loadLeaderboard);
        
        // Add stagger animation to rows
        const observer = new IntersectionObserver(entries => {
            entries.forEach((entry, index) => {
                if (entry.isIntersecting) {
                    setTimeout(() => {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateX(0)';
                    }, index * 50);
                }
            });
        });
        
        // Observe rows when they're added
        const observeRows = () => {
            document.querySelectorAll('.leaderboard-table tr').forEach(row => {
                row.style.opacity = '0';
                row.style.transform = 'translateX(-20px)';
                row.style.transition = 'all 0.5s ease-out';
                observer.observe(row);
            });
        };
        
        // Call observeRows after table is loaded
        const originalLoadLeaderboard = loadLeaderboard;
        loadLeaderboard = async function() {
            await originalLoadLeaderboard();
            setTimeout(observeRows, 100);
        };
    </script>
</body>
</html>