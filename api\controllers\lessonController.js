const databaseService = require('../services/databaseService');
const sessionService = require('../services/sessionService');
const { asyncHandler, NotFoundError, ValidationError } = require('../middleware/errorHandler');
const { SUCCESS_MESSAGES } = require('../config/constants');

class LessonController {
  // Get all lessons with pagination and search
  getAllLessons = asyncHandler(async (req, res) => {
    const { page = 1, limit = 10, search = '', sort = 'order' } = req.query;
    
    const result = await databaseService.getLessons({
      page: parseInt(page),
      limit: parseInt(limit),
      search,
      sort
    });
    
    res.json({
      success: true,
      ...result
    });
  });

  // Get lesson by ID
  getLessonById = asyncHandler(async (req, res) => {
    const { id } = req.params;
    
    const lesson = await databaseService.getLessonById(id);
    
    // Increment view count
    await databaseService.incrementLessonViews(id, lesson.views || 0);
    
    res.json({
      success: true,
      lesson: {
        ...lesson,
        views: (lesson.views || 0) + 1
      }
    });
  });

  // Create new lesson (admin only)
  createLesson = asyncHandler(async (req, res) => {
    const lessonData = req.body;
    
    const newLesson = await databaseService.createLesson(lessonData);
    
    res.status(201).json({
      success: true,
      message: 'Lesson created successfully',
      lesson: newLesson
    });
  });

  // Update lesson (admin only)
  updateLesson = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const updateData = req.body;
    
    const updatedLesson = await databaseService.updateLesson(id, updateData);
    
    res.json({
      success: true,
      message: SUCCESS_MESSAGES.UPDATE_SUCCESS,
      lesson: updatedLesson[0]
    });
  });

  // Delete lesson (admin only)
  deleteLesson = asyncHandler(async (req, res) => {
    const { id } = req.params;
    
    await databaseService.deleteLesson(id);
    
    res.json({
      success: true,
      message: SUCCESS_MESSAGES.DELETE_SUCCESS
    });
  });

  // Update lesson order (admin only)
  updateLessonOrder = asyncHandler(async (req, res) => {
    const { orderedLessons } = req.body;
    
    if (!Array.isArray(orderedLessons)) {
      throw new ValidationError('orderedLessons must be an array');
    }
    
    await databaseService.updateLessonOrder(orderedLessons);
    
    res.json({
      success: true,
      message: 'Lesson order updated successfully'
    });
  });

  // Get lesson statistics
  getLessonStatistics = asyncHandler(async (req, res) => {
    const { id } = req.params;
    
    // Get lesson basic info
    const lesson = await databaseService.getLessonById(id);
    
    // Get lesson results for statistics
    const results = await databaseService.getLessonResults(id);
    
    // Calculate statistics
    const totalAttempts = results.length;
    const averageScore = totalAttempts > 0 ? 
      results.reduce((sum, result) => sum + (result.score || 0), 0) / totalAttempts : 0;
    
    const completionRate = totalAttempts > 0 ? 
      results.filter(result => result.completed).length / totalAttempts * 100 : 0;
    
    const averageTime = totalAttempts > 0 ? 
      results.reduce((sum, result) => sum + (result.timeTaken || 0), 0) / totalAttempts : 0;
    
    const statistics = {
      lessonId: id,
      lessonTitle: lesson.title,
      totalAttempts,
      averageScore: Math.round(averageScore * 100) / 100,
      completionRate: Math.round(completionRate * 100) / 100,
      averageTime: Math.round(averageTime),
      views: lesson.views || 0,
      lastUpdated: lesson.lastUpdated
    };
    
    res.json({
      success: true,
      statistics
    });
  });

  // Get lesson results (admin only)
  getLessonResults = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { limit = 100 } = req.query;
    
    const results = await databaseService.getLessonResults(id);
    
    // Limit results if specified
    const limitedResults = limit ? results.slice(0, parseInt(limit)) : results;
    
    res.json({
      success: true,
      results: limitedResults,
      total: results.length
    });
  });

  // Search lessons
  searchLessons = asyncHandler(async (req, res) => {
    const { q: search = '', page = 1, limit = 10, sort = 'order' } = req.query;
    
    const result = await databaseService.getLessons({
      page: parseInt(page),
      limit: parseInt(limit),
      search,
      sort
    });
    
    res.json({
      success: true,
      ...result
    });
  });

  // Get lessons by subject
  getLessonsBySubject = asyncHandler(async (req, res) => {
    const { subject } = req.params;
    const { page = 1, limit = 10, sort = 'order' } = req.query;
    
    // This would need to be implemented in databaseService
    // For now, use the general getLessons method
    const result = await databaseService.getLessons({
      page: parseInt(page),
      limit: parseInt(limit),
      search: '', // Could filter by subject here
      sort
    });
    
    res.json({
      success: true,
      subject,
      ...result
    });
  });

  // Get lessons by grade
  getLessonsByGrade = asyncHandler(async (req, res) => {
    const { grade } = req.params;
    const { page = 1, limit = 10, sort = 'order' } = req.query;
    
    // This would need to be implemented in databaseService
    // For now, use the general getLessons method
    const result = await databaseService.getLessons({
      page: parseInt(page),
      limit: parseInt(limit),
      search: '', // Could filter by grade here
      sort
    });
    
    res.json({
      success: true,
      grade,
      ...result
    });
  });

  // Get featured lessons
  getFeaturedLessons = asyncHandler(async (req, res) => {
    const { limit = 5 } = req.query;
    
    // Get most popular lessons (by views)
    const result = await databaseService.getLessons({
      page: 1,
      limit: parseInt(limit),
      search: '',
      sort: 'popular'
    });
    
    res.json({
      success: true,
      featured: result.lessons
    });
  });

  // Get recent lessons
  getRecentLessons = asyncHandler(async (req, res) => {
    const { limit = 5 } = req.query;
    
    // Get newest lessons
    const result = await databaseService.getLessons({
      page: 1,
      limit: parseInt(limit),
      search: '',
      sort: 'newest'
    });
    
    res.json({
      success: true,
      recent: result.lessons
    });
  });

  // Duplicate lesson (admin only)
  duplicateLesson = asyncHandler(async (req, res) => {
    const { id } = req.params;
    
    const originalLesson = await databaseService.getLessonById(id);
    
    // Create duplicate with modified title
    const duplicateData = {
      ...originalLesson,
      title: `${originalLesson.title} (Copy)`,
      id: undefined, // Let database generate new ID
      created: undefined, // Will be set by createLesson
      lastUpdated: undefined, // Will be set by createLesson
      views: 0 // Reset views for duplicate
    };
    
    const newLesson = await databaseService.createLesson(duplicateData);
    
    res.status(201).json({
      success: true,
      message: 'Lesson duplicated successfully',
      lesson: newLesson
    });
  });
}

module.exports = new LessonController();
