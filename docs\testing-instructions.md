# Testing Single Session Enforcement

## Quick Fix for Testing

The issue you're experiencing is that the device ID is being generated differently in each browser, even on the same device. I've made the following changes:

### 1. Improved Device ID Stability

**Updated `public/js/device-id.js`:**
- Removed browser-specific characteristics (canvas, audio fingerprints)
- Focus on hardware characteristics that are consistent across browsers
- Use only: screen resolution, timezone, hardware specs, OS info, WebGL renderer

### 2. Temporary Lenient Mode

**Added environment variable support:**
- Set `STRICT_DEVICE_CHECK=false` in your `.env` file to disable strict device checking
- This allows testing session management without device restrictions

### 3. Testing Steps

#### Step 1: Disable Strict Device Checking (Temporary)
Add this line to your `.env` file:
```
STRICT_DEVICE_CHECK=false
```

#### Step 2: Test Session Management
1. Open `/tests/single-session-test.html` in your browser
2. Click "Test Device ID Consistency" to verify device ID generation
3. Click "Show Device Characteristics" to see what's being used
4. Test the login flow:
   - Login with Session A
   - Login with Session B (should terminate Session A)
   - Check Session A status (should be terminated)

#### Step 3: Manual Testing
1. **Browser A**: Login to `/student/login`
2. **Browser B**: Login with same credentials
3. **Browser A**: Try to access any protected page (should redirect to login)
4. **Browser B**: Should have full access

### 4. Expected Behavior

With `STRICT_DEVICE_CHECK=false`:
- ✅ Same student can login from different browsers
- ✅ New login terminates old session
- ✅ Only one active session at a time
- ✅ Session management works correctly

### 5. Debug Information

Check server logs for these messages:
```
🔄 Single session enforcement: Terminating previous session [session_id] for student [student_id]
✅ Previous session [session_id] successfully terminated for student [student_id]
```

### 6. Database Verification

Check the students table:
```sql
SELECT id, full_name, current_session_id, last_login_at 
FROM students 
WHERE phone_number = 'your_test_phone';
```

Only one `current_session_id` should be present per student.

### 7. Re-enable Strict Mode

Once device ID generation is more stable, remove the environment variable or set:
```
STRICT_DEVICE_CHECK=true
```

## Troubleshooting

### If Device IDs are Still Different:
1. Check browser console for device characteristics
2. Compare characteristics between browsers
3. Identify which values are changing
4. Further refine the device ID algorithm

### If Session Management Isn't Working:
1. Check server logs for session termination messages
2. Verify database `current_session_id` updates
3. Check browser network tab for session cookies
4. Verify PostgreSQL session store is working

## Next Steps

1. **Test with lenient mode** to verify session management works
2. **Refine device ID algorithm** to be more consistent across browsers
3. **Re-enable strict mode** once device ID is stable
4. **Monitor production** for any issues

The core session management logic is correct - the issue is just device ID consistency across browsers.
