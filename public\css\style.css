/* ===== MODERN GEN Z DESIGN SYSTEM ===== */

/* Root Variables */
:root {
    /* Primary Colors - Vibrant Gradients */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --success-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --danger-gradient: linear-gradient(135deg, #f43b47 0%, #453a94 100%);
    
    /* Dark Theme Colors */
    --bg-primary: #0a0a0f;
    --bg-secondary: #1a1a2e;
    --bg-tertiary: #16213e;
    --bg-card: rgba(255, 255, 255, 0.05);
    --bg-card-hover: rgba(255, 255, 255, 0.08);
    
    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: #b8bcc8;
    --text-tertiary: #6c757d;
    
    /* Glassmorphism */
    --glass-bg: rgba(255, 255, 255, 0.05);
    --glass-border: rgba(255, 255, 255, 0.1);
    --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    
    /* Neon Effects */
    --neon-purple: #a855f7;
    --neon-pink: #ec4899;
    --neon-blue: #3b82f6;
    --neon-cyan: #06b6d4;
    
    /* Spacing */
    --space-xs: 0.5rem;
    --space-sm: 1rem;
    --space-md: 1.5rem;
    --space-lg: 2rem;
    --space-xl: 3rem;
    --space-2xl: 4rem;
    
    /* Border Radius */
    --radius-sm: 0.5rem;
    --radius-md: 1rem;
    --radius-lg: 1.5rem;
    --radius-xl: 2rem;
    --radius-full: 9999px;
    
    /* Transitions */
    --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    
    /* Z-index */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
}

/* ===== GLOBAL STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
    position: relative;
    min-height: 100vh;
}

/* Background Pattern */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(circle at 20% 50%, rgba(120, 60, 237, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(236, 72, 153, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 20%, rgba(6, 182, 212, 0.3) 0%, transparent 50%);
    z-index: -1;
    animation: backgroundShift 20s ease-in-out infinite;
}

@keyframes backgroundShift {
    0%, 100% { transform: translate(0, 0) scale(1); }
    33% { transform: translate(-20px, -20px) scale(1.1); }
    66% { transform: translate(20px, -10px) scale(0.9); }
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: var(--space-sm);
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

h1 { font-size: clamp(2.5rem, 5vw, 4rem); }
h2 { font-size: clamp(2rem, 4vw, 3rem); }
h3 { font-size: clamp(1.5rem, 3vw, 2rem); }
h4 { font-size: clamp(1.25rem, 2.5vw, 1.5rem); }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

p {
    margin-bottom: var(--space-sm);
    color: var(--text-secondary);
}

a {
    color: var(--neon-blue);
    text-decoration: none;
    transition: var(--transition-fast);
}

a:hover {
    color: var(--neon-cyan);
    text-shadow: 0 0 10px currentColor;
}

/* ===== BUTTONS ===== */
.button, button, .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-xs);
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    font-weight: 600;
    border: none;
    border-radius: var(--radius-full);
    cursor: pointer;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
    text-decoration: none;
    white-space: nowrap;
}

.button::before, button::before, .btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.button:hover::before, button:hover::before, .btn:hover::before {
    width: 300px;
    height: 300px;
}

.button.primary, button.primary, .btn-primary {
    background: var(--primary-gradient);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.button.primary:hover, button.primary:hover, .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

.button.secondary, button.secondary, .btn-secondary {
    background: var(--glass-bg);
    color: var(--text-primary);
    border: 1px solid var(--glass-border);
    backdrop-filter: blur(10px);
}

.button.secondary:hover, button.secondary:hover, .btn-secondary:hover {
    background: var(--glass-bg);
    border-color: var(--neon-purple);
    box-shadow: 0 0 20px rgba(168, 85, 247, 0.4);
}

/* ===== CARDS ===== */
.card {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    padding: var(--space-lg);
    backdrop-filter: blur(10px);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-gradient);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform var(--transition-normal);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--glass-shadow);
    border-color: var(--neon-purple);
}

.card:hover::before {
    transform: scaleX(1);
}

/* ===== FORMS ===== */
input, textarea, select {
    width: 100%;
    padding: 0.75rem 1rem;
    background: var(--glass-bg);
    border: 2px solid var(--glass-border);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-size: 1rem;
    transition: var(--transition-fast);
    backdrop-filter: blur(10px);
}

input:focus, textarea:focus, select:focus {
    outline: none;
    border-color: var(--neon-purple);
    box-shadow: 0 0 0 3px rgba(168, 85, 247, 0.2);
    background: rgba(255, 255, 255, 0.08);
}

.form-group {
    margin-bottom: var(--space-md);
}

.form-group label {
    display: block;
    margin-bottom: var(--space-xs);
    font-weight: 600;
    color: var(--text-primary);
}

/* ===== MODALS ===== */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-normal);
}

.modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--bg-secondary);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    padding: var(--space-xl);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9) translateY(20px);
    transition: var(--transition-normal);
    position: relative;
}

.modal.active .modal-content {
    transform: scale(1) translateY(0);
}

/* ===== ANIMATIONS ===== */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.05); opacity: 0.8; }
}

@keyframes neonGlow {
    0%, 100% { 
        text-shadow: 
            0 0 10px currentColor,
            0 0 20px currentColor,
            0 0 30px currentColor;
    }
    50% { 
        text-shadow: 
            0 0 20px currentColor,
            0 0 30px currentColor,
            0 0 40px currentColor;
    }
}

.fade-in {
    animation: fadeIn 0.6s ease-out forwards;
}

.slide-in {
    animation: slideIn 0.4s ease-out forwards;
}

/* ===== LOADING STATES ===== */
.loading-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-primary);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
}

.spinner {
    width: 60px;
    height: 60px;
    border: 3px solid var(--glass-border);
    border-top-color: var(--neon-purple);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* ===== UTILITIES ===== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mt-1 { margin-top: var(--space-xs); }
.mt-2 { margin-top: var(--space-sm); }
.mt-3 { margin-top: var(--space-md); }
.mt-4 { margin-top: var(--space-lg); }
.mt-5 { margin-top: var(--space-xl); }

.mb-1 { margin-bottom: var(--space-xs); }
.mb-2 { margin-bottom: var(--space-sm); }
.mb-3 { margin-bottom: var(--space-md); }
.mb-4 { margin-bottom: var(--space-lg); }
.mb-5 { margin-bottom: var(--space-xl); }

.hidden { display: none !important; }
.invisible { visibility: hidden !important; }

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
    :root {
        font-size: 14px;
    }
    
    .hide-mobile { display: none !important; }
}

@media (max-width: 480px) {
    :root {
        font-size: 13px;
    }
}

/* ===== HOME BUTTON ===== */
.home-button {
    position: fixed;
    top: 2rem;
    left: 2rem;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--glass-bg);
    border: 2px solid var(--glass-border);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-normal);
    z-index: var(--z-fixed);
    overflow: hidden;
}

.home-button img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.home-button:hover {
    transform: scale(1.1) rotate(5deg);
    border-color: var(--neon-purple);
    box-shadow: 0 0 30px rgba(168, 85, 247, 0.6);
}

/* ===== STAT CARDS ===== */
.stat-card {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    padding: var(--space-lg);
    text-align: center;
    backdrop-filter: blur(10px);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    transform: rotate(45deg);
    transition: var(--transition-slow);
    opacity: 0;
}

.stat-card:hover::before {
    opacity: 1;
}

.stat-card.primary {
    border-color: rgba(102, 126, 234, 0.3);
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
}

.stat-card.success {
    border-color: rgba(67, 233, 123, 0.3);
    background: linear-gradient(135deg, rgba(67, 233, 123, 0.1) 0%, rgba(56, 249, 215, 0.1) 100%);
}

.stat-card.warning {
    border-color: rgba(250, 112, 154, 0.3);
    background: linear-gradient(135deg, rgba(250, 112, 154, 0.1) 0%, rgba(254, 225, 64, 0.1) 100%);
}

.stat-card.info {
    border-color: rgba(79, 172, 254, 0.3);
    background: linear-gradient(135deg, rgba(79, 172, 254, 0.1) 0%, rgba(0, 242, 254, 0.1) 100%);
}

.stat-icon {
    font-size: 3rem;
    margin-bottom: var(--space-sm);
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--space-xs);
}

.stat-label {
    font-size: 1rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* ===== TABLES ===== */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    margin: var(--space-md) 0;
}

table {
    width: 100%;
    border-collapse: collapse;
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border-radius: var(--radius-md);
    overflow: hidden;
}

th, td {
    padding: var(--space-sm) var(--space-md);
    text-align: left;
    border-bottom: 1px solid var(--glass-border);
}

th {
    background: rgba(255, 255, 255, 0.05);
    font-weight: 600;
    color: var(--text-primary);
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.05em;
}

tr:hover {
    background: rgba(255, 255, 255, 0.03);
}

/* ===== NETWORK CANVAS ===== */
#network-canvas {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    opacity: 0.5;
}

/* ===== SEARCH BOX ===== */
.search-box {
    position: relative;
    margin-bottom: var(--space-md);
}

.search-box input {
    padding-left: 3rem;
    background: var(--glass-bg);
    border: 2px solid var(--glass-border);
    color: var(--text-primary);
}

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-tertiary);
}

/* ===== PAGINATION ===== */
.pagination {
    display: flex;
    justify-content: center;
    gap: var(--space-xs);
    margin-top: var(--space-lg);
}

.pagination button {
    padding: 0.5rem 1rem;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    color: var(--text-primary);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition-fast);
}

.pagination button:hover:not(:disabled) {
    background: var(--glass-bg);
    border-color: var(--neon-purple);
    transform: translateY(-2px);
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* ===== ERROR MESSAGES ===== */
.error-message, .error {
    background: rgba(244, 59, 71, 0.1);
    border: 1px solid rgba(244, 59, 71, 0.3);
    color: #ff6b6b;
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius-md);
    margin-bottom: var(--space-md);
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.success-message, .success {
    background: rgba(67, 233, 123, 0.1);
    border: 1px solid rgba(67, 233, 123, 0.3);
    color: #43e97b;
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius-md);
    margin-bottom: var(--space-md);
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

/* ===== RESPONSIVE GRID ===== */
.grid {
    display: grid;
    gap: var(--space-md);
}

.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

@media (max-width: 768px) {
    .grid-cols-2,
    .grid-cols-3,
    .grid-cols-4 {
        grid-template-columns: 1fr;
    }
}

/* ===== BADGES ===== */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
    font-weight: 600;
    border-radius: var(--radius-full);
    background: var(--primary-gradient);
    color: white;
}

.badge.secondary {
    background: var(--secondary-gradient);
}

.badge.success {
    background: var(--success-gradient);
}

.badge.warning {
    background: var(--warning-gradient);
}

.badge.danger {
    background: var(--danger-gradient);
}

/* ===== TOOLTIPS ===== */
[data-tooltip] {
    position: relative;
    cursor: pointer;
}

[data-tooltip]::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    padding: 0.5rem 1rem;
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: var(--transition-fast);
    margin-bottom: 0.5rem;
    box-shadow: var(--glass-shadow);
}

[data-tooltip]:hover::after {
    opacity: 1;
}

/* ===== SCROLLBAR ===== */
::-webkit-scrollbar {
    width: 12px;
    height: 12px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--glass-bg);
    border-radius: var(--radius-full);
    border: 2px solid var(--bg-secondary);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--glass-border);
}

/* ===== SELECTION ===== */
::selection {
    background: var(--neon-purple);
    color: white;
}

::-moz-selection {
    background: var(--neon-purple);
    color: white;
}

/* ===== ADDITIONAL MODERN STYLES FOR CONSISTENCY ===== */

/* Quiz Game Specific */
.quiz-game-container {
    min-height: 100vh;
    padding: 2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
}

.score-display {
    position: fixed;
    top: 2rem;
    right: 2rem;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    padding: 1.5rem 2rem;
    backdrop-filter: blur(20px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    z-index: 100;
    animation: slideIn 0.6s ease-out;
}

.score-content {
    display: flex;
    align-items: baseline;
    gap: 0.5rem;
}

.score-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.score-value {
    font-size: 2.5rem;
    font-weight: 800;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: pulse 2s infinite;
}

.score-total {
    font-size: 1.2rem;
    color: var(--text-tertiary);
}

.quiz-info {
    position: fixed;
    top: 2rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 2rem;
    z-index: 100;
}

.question-counter, .timer-display {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-full);
    padding: 0.75rem 1.5rem;
    backdrop-filter: blur(20px);
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.timer-display {
    background: linear-gradient(135deg, rgba(244, 59, 71, 0.1) 0%, rgba(245, 87, 108, 0.1) 100%);
    border-color: rgba(244, 59, 71, 0.3);
}

.timer-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #ff6b6b;
}

.progress-container {
    width: 100%;
    max-width: 800px;
    margin-bottom: 3rem;
}

.progress-bar {
    position: relative;
    height: 30px;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-full);
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.progress {
    height: 100%;
    background: var(--primary-gradient);
    width: 0%;
    transition: width 0.5s ease-out;
    position: relative;
    box-shadow: 0 0 20px rgba(168, 85, 247, 0.6);
}

.milestone {
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%);
    background: var(--bg-secondary);
    border: 2px solid var(--glass-border);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    color: var(--text-primary);
    z-index: 1;
}

.question-container {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    padding: 3rem;
    max-width: 800px;
    width: 100%;
    backdrop-filter: blur(20px);
    box-shadow: var(--glass-shadow);
    animation: fadeIn 0.6s ease-out;
}

.question-text {
    font-size: 1.8rem;
    margin-bottom: 2rem;
    text-align: center;
    color: var(--text-primary);
    line-height: 1.6;
}

.options-container {
    display: flex;
    gap: 2rem;
    justify-content: center;
}

.option-btn {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 3px solid var(--glass-border);
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 3rem;
    color: var(--text-primary);
    position: relative;
    overflow: hidden;
}

.option-btn.true-btn {
    background: linear-gradient(135deg, rgba(67, 233, 123, 0.1) 0%, rgba(56, 249, 215, 0.1) 100%);
    border-color: rgba(67, 233, 123, 0.3);
}

.option-btn.true-btn:hover {
    transform: scale(1.1);
    background: var(--success-gradient);
    color: white;
    box-shadow: 0 10px 30px rgba(67, 233, 123, 0.5);
}

.option-btn.false-btn {
    background: linear-gradient(135deg, rgba(244, 59, 71, 0.1) 0%, rgba(245, 87, 108, 0.1) 100%);
    border-color: rgba(244, 59, 71, 0.3);
}

.option-btn.false-btn:hover {
    transform: scale(1.1);
    background: var(--danger-gradient);
    color: white;
    box-shadow: 0 10px 30px rgba(244, 59, 71, 0.5);
}

.celebration-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.celebration-content {
    background: var(--glass-bg);
    border: 2px solid var(--glass-border);
    border-radius: var(--radius-xl);
    padding: 3rem 4rem;
    text-align: center;
    backdrop-filter: blur(20px);
    box-shadow: 0 20px 60px rgba(168, 85, 247, 0.4);
}

.celebration-content h2 {
    font-size: 3rem;
    margin-bottom: 1rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.celebration-content p {
    font-size: 1.5rem;
    color: var(--text-primary);
}

.milestone-number {
    font-weight: 700;
    color: var(--neon-purple);
    text-shadow: 0 0 20px rgba(168, 85, 247, 0.8);
}

/* Admin Pages Styling */
.admin-container, .lesson-editor, .statistics-container, .history-container {
    min-height: 100vh;
    padding: 7rem 2rem 2rem;
    max-width: 1400px;
    margin: 0 auto;
    animation: fadeIn 0.6s ease-out;
}

.admin-container h1, .editor-header h1, .stats-header h1, .history-header h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    text-align: center;
    margin-bottom: 3rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.admin-controls {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 3rem;
}

.admin-nav {
    display: flex;
    justify-content: center;
    margin-bottom: 3rem;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 2rem;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-full);
    color: var(--text-primary);
    text-decoration: none;
    transition: var(--transition-normal);
    backdrop-filter: blur(10px);
}

.nav-item:hover {
    background: var(--glass-bg);
    border-color: var(--neon-purple);
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(168, 85, 247, 0.3);
    color: var(--neon-purple);
}

.nav-item i {
    font-size: 1.2rem;
}

#lesson-list {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    padding: 2rem;
    backdrop-filter: blur(20px);
}

#lesson-list h2 {
    margin-bottom: 2rem;
    font-size: 2rem;
}

.lesson-item {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    margin-bottom: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: var(--transition-normal);
}

.lesson-item:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: var(--neon-purple);
    transform: translateX(5px);
    box-shadow: 0 5px 20px rgba(168, 85, 247, 0.2);
}

.lesson-info h3 {
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.lesson-meta {
    display: flex;
    gap: 1rem;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.lesson-actions {
    display: flex;
    gap: 0.5rem;
}

/* Configuration Form Styling */
.configuration-form {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    padding: 2rem;
    backdrop-filter: blur(20px);
    max-width: 1000px;
    margin: 0 auto;
}

.configuration-form h2 {
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
    color: var(--text-primary);
    border-bottom: 1px solid var(--glass-border);
    padding-bottom: 1rem;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.modern-input, .modern-select, .modern-textarea {
    width: 100%;
    padding: 1rem 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid var(--glass-border);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-size: 1rem;
    transition: var(--transition-fast);
}

.modern-input:focus, .modern-select:focus, .modern-textarea:focus {
    outline: none;
    border-color: var(--neon-purple);
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 0 20px rgba(168, 85, 247, 0.3);
}

.image-upload-container {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.image-upload-container input[type="file"] {
    flex: 1;
}

.remove-image-btn {
    padding: 0.5rem 1rem;
    background: var(--danger-gradient);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition-fast);
}

.remove-image-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(244, 59, 71, 0.4);
}

.help-text {
    font-size: 0.9rem;
    color: var(--text-tertiary);
    margin-left: 0.5rem;
}

.tags-input-container {
    position: relative;
}

.tags-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.tag-item {
    padding: 0.5rem 1rem;
    background: var(--primary-gradient);
    color: white;
    border-radius: var(--radius-full);
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    animation: fadeIn 0.3s ease-out;
}

.tag-item button {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    font-size: 1.2rem;
    padding: 0;
    margin: 0;
    line-height: 1;
}

/* Statistics Page Enhancements */
.stats-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 3rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.export-controls {
    display: flex;
    gap: 1rem;
}

.export-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: var(--primary-gradient);
    color: white;
    border: none;
    border-radius: var(--radius-full);
    cursor: pointer;
    font-weight: 600;
    transition: var(--transition-normal);
}

.export-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(168, 85, 247, 0.4);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.stats-content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.stats-card, .history-card {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    padding: 2rem;
    backdrop-filter: blur(20px);
    box-shadow: var(--glass-shadow);
}

.stats-card h3, .history-card h3 {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
    color: var(--text-primary);
}

.stats-card h3 i, .history-card h3 i {
    color: var(--neon-purple);
}

.chart-container {
    position: relative;
    height: 300px;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 1rem;
}

/* Table Styling */
.statistics-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background: transparent;
}

.statistics-table th {
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.05em;
    padding: 1rem;
    text-align: left;
    border-bottom: 2px solid var(--glass-border);
}

.statistics-table td {
    padding: 1rem;
    color: var(--text-secondary);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.statistics-table tr:hover {
    background: rgba(255, 255, 255, 0.03);
}

/* History Page Specific */
.history-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.header-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.delete-all-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: var(--danger-gradient);
    color: white;
    border: none;
    border-radius: var(--radius-full);
    cursor: pointer;
    font-weight: 600;
    transition: var(--transition-normal);
}

.delete-all-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(244, 59, 71, 0.4);
}

.clear-btn {
    background: transparent;
    border: none;
    color: var(--text-tertiary);
    cursor: pointer;
    padding: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
}

.clear-btn:hover {
    color: var(--neon-purple);
}

.sortable {
    cursor: pointer;
    user-select: none;
}

.sortable i {
    margin-left: 0.5rem;
    font-size: 0.8rem;
    color: var(--text-tertiary);
}

.sortable:hover {
    color: var(--neon-purple);
}

/* Pagination */
.pagination-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    margin-top: 2rem;
}

.pagination-container button {
    padding: 0.5rem 1rem;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    color: var(--text-primary);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition-fast);
}

.pagination-container button:hover:not(:disabled) {
    background: var(--glass-bg);
    border-color: var(--neon-purple);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(168, 85, 247, 0.3);
}

.pagination-container button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-container button.active {
    background: var(--primary-gradient);
    color: white;
    border-color: transparent;
}

/* Gallery Specific Styles */
.gallery-container {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.gallery-layout {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    padding: 2rem;
    max-width: 1200px;
    width: 100%;
    backdrop-filter: blur(20px);
    box-shadow: var(--glass-shadow);
}

.gallery-main {
    position: relative;
    margin-bottom: 2rem;
}

.gallery-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: var(--glass-bg);
    border: 2px solid var(--glass-border);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 1.5rem;
    color: var(--text-primary);
    transition: var(--transition-fast);
    backdrop-filter: blur(10px);
    z-index: 10;
}

.gallery-arrow:hover {
    background: var(--primary-gradient);
    color: white;
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 10px 30px rgba(168, 85, 247, 0.4);
}

.prev-arrow {
    left: -25px;
}

.next-arrow {
    right: -25px;
}

.gallery-content {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 500px;
    background: rgba(255, 255, 255, 0.02);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.gallery-content img {
    max-width: 100%;
    max-height: 500px;
    object-fit: contain;
    cursor: zoom-in;
    transition: var(--transition-normal);
}

.image-counter {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-full);
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    color: var(--text-primary);
    backdrop-filter: blur(10px);
}

.gallery-preview {
    overflow-x: auto;
    padding: 0.5rem 0;
}

.preview-strip {
    display: flex;
    gap: 0.5rem;
    padding: 0.5rem;
}

.preview-item {
    flex-shrink: 0;
    width: 80px;
    height: 80px;
    border: 2px solid var(--glass-border);
    border-radius: var(--radius-md);
    overflow: hidden;
    cursor: pointer;
    transition: var(--transition-fast);
    opacity: 0.6;
}

.preview-item:hover {
    opacity: 1;
    transform: scale(1.05);
    border-color: var(--neon-purple);
    box-shadow: 0 5px 15px rgba(168, 85, 247, 0.3);
}

.preview-item.active {
    opacity: 1;
    border-color: var(--neon-purple);
    box-shadow: 0 0 20px rgba(168, 85, 247, 0.5);
}

.preview-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Image Modal */
.image-modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
}

.image-modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-image {
    max-width: 90%;
    max-height: 90%;
    object-fit: contain;
    border-radius: var(--radius-lg);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.close-modal {
    position: absolute;
    top: 2rem;
    right: 2rem;
    font-size: 3rem;
    color: var(--text-primary);
    cursor: pointer;
    transition: var(--transition-fast);
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
}

.close-modal:hover {
    transform: rotate(90deg) scale(1.1);
    color: var(--neon-purple);
    border-color: var(--neon-purple);
}

/* Admin Quiz Edit Styles */
.sortable-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

.sortable-container h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 2rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.questions-header {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 1rem;
}

.minimize-all-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-full);
    color: var(--text-primary);
    cursor: pointer;
    transition: var(--transition-fast);
    backdrop-filter: blur(10px);
}

.minimize-all-btn:hover {
    background: var(--glass-bg);
    border-color: var(--neon-purple);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(168, 85, 247, 0.3);
}

.add-question-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    width: 100%;
    padding: 1.5rem;
    margin-top: 2rem;
    background: var(--glass-bg);
    border: 2px dashed var(--glass-border);
    border-radius: var(--radius-lg);
    color: var(--text-primary);
    cursor: pointer;
    transition: var(--transition-normal);
    font-weight: 600;
    font-size: 1.1rem;
}

.add-question-btn:hover {
    background: rgba(168, 85, 247, 0.1);
    border-color: var(--neon-purple);
    color: var(--neon-purple);
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(168, 85, 247, 0.3);
}

.text-editor-panel {
    position: fixed;
    right: 0;
    top: 0;
    height: 100vh;
    width: 400px;
    background: var(--bg-secondary);
    border-left: 1px solid var(--glass-border);
    transform: translateX(350px);
    transition: transform var(--transition-normal);
    z-index: 1000;
}

.text-editor-panel.active {
    transform: translateX(0);
}

.text-editor-toggle {
    position: absolute;
    left: -50px;
    top: 50%;
    transform: translateY(-50%);
    width: 50px;
    height: 100px;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-right: none;
    border-radius: var(--radius-lg) 0 0 var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    backdrop-filter: blur(10px);
    transition: var(--transition-fast);
}

.text-editor-toggle:hover {
    background: rgba(168, 85, 247, 0.1);
    border-color: var(--neon-purple);
}

.text-editor-content {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.text-editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background: var(--glass-bg);
    border-bottom: 1px solid var(--glass-border);
}

.text-editor-header h3 {
    margin: 0;
    font-size: 1.2rem;
}

.render-btn {
    padding: 0.5rem 1rem;
    background: var(--primary-gradient);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    font-weight: 600;
    transition: var(--transition-fast);
}

.render-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(168, 85, 247, 0.4);
}

.text-editor-container {
    flex: 1;
    padding: 1rem;
}

#text-editor {
    width: 100%;
    height: 100%;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-md);
    padding: 1rem;
    color: var(--text-primary);
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    resize: none;
}

/* Mobile Responsive Adjustments */
@media (max-width: 768px) {
    .quiz-info {
        flex-direction: column;
        gap: 1rem;
        position: relative;
        top: auto;
        left: auto;
        transform: none;
        margin-bottom: 2rem;
    }
    
    .score-display {
        position: relative;
        top: auto;
        right: auto;
        margin-bottom: 1rem;
    }
    
    .options-container {
        flex-direction: column;
        gap: 1rem;
    }
    
    .option-btn {
        width: 100px;
        height: 100px;
        font-size: 2.5rem;
    }
    
    .stats-grid, .history-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-content-grid {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .text-editor-panel {
        width: 100%;
        transform: translateX(100%);
    }
    
    .text-editor-toggle {
        display: none;
    }
    
    .gallery-arrow {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }
    
    .mobile-hide {
        display: none;
    }
    
    .mobile-optional {
        display: none;
    }
    
    .header-controls {
        flex-direction: column;
        width: 100%;
    }
    
    .search-box {
        width: 100%;
    }
}

/* Loading Animation Improvements */
.loading-indicator {
    background: rgba(10, 10, 15, 0.95);
    backdrop-filter: blur(10px);
}

.loading-indicator p {
    margin-top: 1rem;
    font-size: 1.1rem;
    color: var(--text-primary);
    animation: pulse 2s infinite;
}

/* Review Lesson Modal Styling */
#review-lesson-modal {
    backdrop-filter: blur(10px);
}

#review-lesson-modal > div {
    background: var(--bg-secondary) !important;
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl) !important;
    padding: 2rem !important;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

#review-lesson-modal h2 {
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    font-size: 1.8rem;
}

#review-lesson-modal label {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: block;
}

#review-lesson-modal input,
#review-lesson-modal select {
    width: 100%;
    padding: 0.75rem 1rem;
    background: var(--glass-bg);
    border: 2px solid var(--glass-border);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-size: 1rem;
    transition: var(--transition-fast);
    margin-bottom: 1rem;
}

#review-lesson-modal input:focus,
#review-lesson-modal select:focus {
    outline: none;
    border-color: var(--neon-purple);
    box-shadow: 0 0 20px rgba(168, 85, 247, 0.3);
}

#review-lesson-modal .button {
    width: 100%;
    margin-top: 0.5rem;
}

#review-lesson-modal > div > span {
    color: var(--text-tertiary);
    transition: var(--transition-fast);
}

#review-lesson-modal > div > span:hover {
    color: var(--neon-purple);
    transform: rotate(90deg);
}

/* Fireworks Container */
.fireworks-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 3000;
}

.firework-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

/* Additional Hover Effects */
.button:active, button:active, .btn:active {
    transform: scale(0.98);
}

/* Smooth Scrollbar for Gallery */
.gallery-preview::-webkit-scrollbar {
    height: 8px;
}

.gallery-preview::-webkit-scrollbar-track {
    background: var(--glass-bg);
    border-radius: var(--radius-full);
}

.gallery-preview::-webkit-scrollbar-thumb {
    background: var(--glass-border);
    border-radius: var(--radius-full);
}

.gallery-preview::-webkit-scrollbar-thumb:hover {
    background: var(--neon-purple);
}

/* Message Styles */
.message {
    padding: 1rem 1.5rem;
    border-radius: var(--radius-md);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    animation: slideIn 0.5s ease-out;
}

.success-message {
    background: rgba(67, 233, 123, 0.1);
    border: 1px solid rgba(67, 233, 123, 0.3);
    color: #43e97b;
}

.error-message {
    background: rgba(244, 59, 71, 0.1);
    border: 1px solid rgba(244, 59, 71, 0.3);
    color: #ff6b6b;
}

/* Make tables more modern */
table {
    border-radius: var(--radius-lg);
    overflow: hidden;
}

th:first-child {
    border-top-left-radius: var(--radius-lg);
}

th:last-child {
    border-top-right-radius: var(--radius-lg);
}

/* Input animations */
input, textarea, select {
    transition: all var(--transition-fast);
}

input:focus, textarea:focus, select:focus {
    transform: translateY(-2px);
}

/* Button group styling */
.modal-buttons {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

.modal-buttons button {
    flex: 1;
}

/* Add subtle animations to stat cards */
.stat-card {
    transition: all var(--transition-normal);
    cursor: pointer;
}

.stat-card:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

.stat-card:hover .stat-icon {
    transform: scale(1.1);
    animation: pulse 1s infinite;
}

.stat-icon {
    transition: transform var(--transition-fast);
}

/* ===== PROFILE PAGE STYLES ===== */
.profile-container {
    max-width: 1000px;
    margin: 7rem auto 2rem;
    padding: 2rem;
    animation: fadeIn 0.6s ease-out;
}

.profile-header {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    padding: 2.5rem;
    backdrop-filter: blur(20px);
    display: flex;
    align-items: center;
    gap: 2rem;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.profile-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
}

.profile-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: var(--primary-gradient);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    font-weight: 800;
    box-shadow: 0 10px 30px rgba(168, 85, 247, 0.4);
    animation: pulse 3s infinite;
}

.profile-info h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.profile-info p {
    color: var(--text-secondary);
    font-size: 1.1rem;
}

.profile-rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.3rem;
    font-weight: 700;
}

.profile-rating .fas {
    color: var(--neon-purple);
    filter: drop-shadow(0 0 5px currentColor);
}

.profile-section {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    padding: 2rem;
    margin-bottom: 2rem;
    backdrop-filter: blur(20px);
    transition: var(--transition-normal);
}

.profile-section:hover {
    border-color: var(--neon-purple);
    box-shadow: 0 10px 30px rgba(168, 85, 247, 0.2);
}

.profile-section h2 {
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.chart-container {
    position: relative;
    width: 100%;
    height: 350px;
    margin: 1.5rem 0;
    background: rgba(255, 255, 255, 0.02);
    border-radius: var(--radius-lg);
    padding: 1rem;
}

.rating-tiers {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1.5rem;
    justify-content: center;
}

.rating-tier {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-full);
    font-size: 0.9rem;
    transition: var(--transition-fast);
}

.rating-tier:hover {
    transform: translateY(-2px);
    border-color: var(--neon-purple);
    box-shadow: 0 5px 15px rgba(168, 85, 247, 0.3);
}

.tier-color {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    box-shadow: 0 0 10px currentColor;
}

.rating-history-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.history-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--glass-border);
    transition: var(--transition-fast);
}

.history-item:hover {
    background: rgba(255, 255, 255, 0.03);
    padding-left: 2rem;
}

.history-item:last-child {
    border-bottom: none;
}

.history-details strong {
    color: var(--text-primary);
    font-size: 1.1rem;
}

.history-details span {
    display: block;
    color: var(--text-tertiary);
    font-size: 0.9rem;
    margin-top: 0.25rem;
}

.history-change {
    font-weight: 700;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-full);
    font-size: 1rem;
    min-width: 80px;
    text-align: center;
}

.history-change.positive {
    background: rgba(67, 233, 123, 0.1);
    border: 1px solid rgba(67, 233, 123, 0.3);
    color: #43e97b;
}

.history-change.negative {
    background: rgba(244, 59, 71, 0.1);
    border: 1px solid rgba(244, 59, 71, 0.3);
    color: #ff6b6b;
}

.no-data {
    padding: 3rem;
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
}

/* ===== LEADERBOARD PAGE STYLES ===== */
.leaderboard-container {
    max-width: 1200px;
    margin: 7rem auto 2rem;
    padding: 2rem;
    animation: fadeIn 0.6s ease-out;
}

.leaderboard-header {
    text-align: center;
    margin-bottom: 3rem;
    animation: fadeIn 0.8s ease-out;
}

.leaderboard-header h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    margin-bottom: 1rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.leaderboard-header p {
    color: var(--text-secondary);
    font-size: 1.2rem;
}

.leaderboard-filters {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 3rem;
    animation: fadeIn 1s ease-out;
}

.filter-button {
    padding: 0.75rem 2rem;
    font-size: 1rem;
    border: 2px solid var(--glass-border);
    border-radius: var(--radius-full);
    background: var(--glass-bg);
    color: var(--text-primary);
    cursor: pointer;
    transition: var(--transition-normal);
    backdrop-filter: blur(10px);
    font-weight: 600;
}

.filter-button:hover {
    border-color: var(--neon-purple);
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(168, 85, 247, 0.3);
}

.filter-button.active {
    background: var(--primary-gradient);
    color: white;
    border-color: transparent;
    box-shadow: 0 10px 30px rgba(168, 85, 247, 0.4);
}

.table-responsive-wrapper {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    padding: 0;
    backdrop-filter: blur(20px);
    overflow: hidden;
    box-shadow: var(--glass-shadow);
    animation: fadeIn 1.2s ease-out;
}

.leaderboard-table {
    width: 100%;
    border-collapse: collapse;
    background: transparent;
}

.leaderboard-table th {
    background: rgba(255, 255, 255, 0.05);
    padding: 1.5rem;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    color: var(--text-primary);
    text-align: left;
    border-bottom: 2px solid var(--glass-border);
    font-weight: 700;
}

.leaderboard-table td {
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    color: var(--text-secondary);
    vertical-align: middle;
    transition: var(--transition-fast);
}

.leaderboard-table tr {
    transition: var(--transition-fast);
}

.leaderboard-table tr:hover {
    background: rgba(255, 255, 255, 0.03);
}

.leaderboard-table tr:hover td {
    color: var(--text-primary);
}

.rank {
    font-weight: 700;
    font-size: 1.2rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.top-3 .rank {
    font-size: 1.5rem;
    font-weight: 800;
}

.medal {
    margin-right: 0.5rem;
    font-size: 1.5rem;
    filter: drop-shadow(0 0 5px currentColor);
    animation: pulse 2s infinite;
}

.gold { color: #FFD700; }
.silver { color: #C0C0C0; }
.bronze { color: #CD7F32; }

.user-profile {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-avatar {
    width: 45px;
    height: 45px;
    min-width: 45px;
    font-size: 1.2rem;
    font-weight: 700;
    background: var(--primary-gradient);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 5px 15px rgba(168, 85, 247, 0.3);
}

.user-profile a {
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition-fast);
}

.user-profile a:hover {
    color: var(--neon-purple);
    text-shadow: 0 0 10px rgba(168, 85, 247, 0.5);
}

.rating-value {
    font-weight: 700;
    font-size: 1.1rem;
    color: var(--text-primary);
}

.rating-change {
    font-weight: 700;
    font-size: 1rem;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-full);
}

.rating-change.positive {
    background: rgba(67, 233, 123, 0.1);
    border: 1px solid rgba(67, 233, 123, 0.3);
    color: #43e97b;
}

.rating-change.negative {
    background: rgba(244, 59, 71, 0.1);
    border: 1px solid rgba(244, 59, 71, 0.3);
    color: #ff6b6b;
}

/* ===== LESSON PAGE STYLES ===== */
.lesson-container {
    min-height: 100vh;
    padding: 7rem 2rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
    animation: fadeIn 0.6s ease-out;
}

#lesson-title {
    text-align: center;
    font-size: clamp(2.5rem, 5vw, 4rem);
    margin-bottom: 3rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: fadeIn 0.8s ease-out;
}

.lesson-image-container {
    margin-bottom: 3rem;
    text-align: center;
    animation: fadeIn 1s ease-out;
}

#lesson-image {
    max-width: 100%;
    height: auto;
    border-radius: var(--radius-xl);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    cursor: zoom-in;
    transition: var(--transition-normal);
}

#lesson-image:hover {
    transform: scale(1.02);
    box-shadow: 0 30px 60px rgba(168, 85, 247, 0.4);
}

/* Question sections styling */
#abcd-questions, #truefalse-questions, #number-questions {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    padding: 2.5rem;
    margin-bottom: 2rem;
    backdrop-filter: blur(20px);
    animation: fadeIn 1.2s ease-out;
}

#abcd-questions h3, #truefalse-questions h3, #number-questions h3 {
    font-size: 2rem;
    margin-bottom: 2rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

#submit-quiz-btn {
    display: block;
    margin: 3rem auto;
    padding: 1rem 3rem;
    background: var(--primary-gradient);
    color: white;
    border: none;
    border-radius: var(--radius-full);
    font-size: 1.2rem;
    font-weight: 700;
    cursor: pointer;
    transition: var(--transition-normal);
    box-shadow: 0 10px 30px rgba(168, 85, 247, 0.4);
    animation: fadeIn 1.4s ease-out;
}

#submit-quiz-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(168, 85, 247, 0.6);
}

/* ===== SHARE LESSON PAGE STYLES ===== */
.share-container {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    padding: 3rem;
    max-width: 700px;
    width: 90%;
    backdrop-filter: blur(20px);
    box-shadow: var(--glass-shadow);
    text-align: center;
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
    animation: fadeIn 0.6s ease-out forwards;
}

.share-container.loaded {
    opacity: 1;
}

.share-container h1 {
    font-size: 2.5rem;
    margin-bottom: 2rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.share-container .lesson-image {
    max-width: 100%;
    height: auto;
    max-height: 300px;
    border-radius: var(--radius-lg);
    margin-bottom: 2rem;
    object-fit: cover;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.share-container .info {
    text-align: left;
    margin-bottom: 2.5rem;
    font-size: 1.1rem;
    color: var(--text-secondary);
}

.share-container .info p {
    margin: 1rem 0;
    display: flex;
    align-items: center;
}

.share-container .info svg {
    width: 24px;
    height: 24px;
    margin-right: 1rem;
    fill: var(--neon-purple);
}

.share-container .start-button {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    background: var(--primary-gradient);
    color: white;
    padding: 1rem 2.5rem;
    border: none;
    border-radius: var(--radius-full);
    font-size: 1.2rem;
    font-weight: 700;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-normal);
    box-shadow: 0 10px 30px rgba(168, 85, 247, 0.4);
}

.share-container .start-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(168, 85, 247, 0.6);
}

.user-history-section {
    margin-top: 3rem;
}

.user-history-section h2 {
    text-align: left;
    margin-bottom: 1.5rem;
    font-size: 1.8rem;
    color: var(--text-primary);
    padding-top: 2rem;
    border-top: 1px solid var(--glass-border);
}

.user-history-section .history-card {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    margin-bottom: 1rem;
    text-align: left;
    transition: var(--transition-normal);
}

.user-history-section .history-card:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: var(--neon-purple);
    transform: translateX(5px);
    box-shadow: 0 5px 20px rgba(168, 85, 247, 0.2);
}

.user-history-section .history-card p {
    margin: 0.5rem 0;
    font-size: 1rem;
    color: var(--text-secondary);
}

.user-history-section .history-card .score-line {
    font-size: 1.3rem;
    font-weight: 700;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.user-history-section .history-card .details-link {
    display: inline-block;
    margin-top: 1rem;
    font-size: 1rem;
    color: var(--neon-purple);
    text-decoration: none;
    transition: var(--transition-fast);
}

.user-history-section .history-card .details-link:hover {
    color: var(--neon-blue);
    text-shadow: 0 0 10px currentColor;
}

/* ===== RESULTS PAGE STYLES ===== */
.results-container {
    min-height: 100vh;
    padding: 7rem 2rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
    animation: fadeIn 0.6s ease-out;
    position: relative;
}

.results-header {
    text-align: center;
    margin-bottom: 3rem;
    animation: fadeIn 0.8s ease-out;
}

.results-header h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    margin-bottom: 1rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.results-content {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    padding: 2rem;
    backdrop-filter: blur(20px);
    animation: fadeIn 1.2s ease-out;
}

.sort-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.sort-btn {
    padding: 0.75rem 2rem;
    background: transparent;
    border: 2px solid var(--glass-border);
    border-radius: var(--radius-full);
    color: var(--text-primary);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-normal);
}

.sort-btn:hover {
    border-color: var(--neon-purple);
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(168, 85, 247, 0.3);
}

.sort-btn.active {
    background: var(--primary-gradient);
    color: white;
    border-color: transparent;
    box-shadow: 0 10px 30px rgba(168, 85, 247, 0.4);
}

.results-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

/* Question result cards */
.question-result {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    transition: var(--transition-normal);
    animation: fadeIn 0.6s ease-out;
}

.question-result:hover {
    background: rgba(255, 255, 255, 0.05);
    transform: translateX(5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.question-result.correct {
    border-left: 4px solid #43e97b;
}

.question-result.incorrect {
    border-left: 4px solid #ff6b6b;
}

.question-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.question-number {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--text-primary);
}

.result-icon {
    font-size: 1.5rem;
    animation: pulse 2s infinite;
}

.result-icon.correct {
    color: #43e97b;
}

.result-icon.incorrect {
    color: #ff6b6b;
}

.question-text {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: 1rem;
    line-height: 1.6;
}

.question-image {
    max-width: 100%;
    margin: 1rem 0;
    border-radius: var(--radius-md);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    cursor: zoom-in;
    transition: var(--transition-normal);
}

.question-image:hover {
    transform: scale(1.02);
    box-shadow: 0 10px 30px rgba(168, 85, 247, 0.3);
}

/* Multiple choice options styling */
.multiple-choice-options {
    margin-top: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.option-item {
    padding: 1rem 1.5rem;
    border: 2px solid var(--glass-border);
    border-radius: var(--radius-md);
    background: var(--glass-bg);
    color: var(--text-secondary);
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    gap: 1rem;
    position: relative;
    overflow: hidden;
}

.option-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: transparent;
    transition: var(--transition-fast);
}

.mc-icon {
    font-size: 1.2rem;
    min-width: 20px;
    text-align: center;
}

.option-item.correct-option {
    background: rgba(67, 233, 123, 0.1);
    border-color: rgba(67, 233, 123, 0.3);
    color: #43e97b;
}

.option-item.correct-option::before {
    background: #43e97b;
}

.option-item.correct-option .mc-icon {
    color: #43e97b;
    filter: drop-shadow(0 0 5px currentColor);
}

.option-item.incorrect-selected {
    background: rgba(244, 59, 71, 0.1);
    border-color: rgba(244, 59, 71, 0.3);
    color: #ff6b6b;
}

.option-item.incorrect-selected::before {
    background: #ff6b6b;
}

.option-item.incorrect-selected .mc-icon {
    color: #ff6b6b;
    filter: drop-shadow(0 0 5px currentColor);
}

/* Answer section */
.answer-section {
    margin-top: 1.5rem;
    padding: 1.5rem;
    background: rgba(168, 85, 247, 0.05);
    border: 1px solid rgba(168, 85, 247, 0.2);
    border-radius: var(--radius-md);
}

.answer-section h4 {
    color: var(--neon-purple);
    margin-bottom: 1rem;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.answer-section h4 i {
    font-size: 1rem;
}

/* User answer display */
.user-answer {
    margin-top: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-md);
}

.user-answer-label {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.user-answer-value {
    color: var(--text-secondary);
}

/* Rank display animations and styles */
#user-rank {
    transition: all 0.5s ease;
    min-height: 2.5em;
}

.rank-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.2em;
    opacity: 0;
    transform: scale(0.5);
    transition: all 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.numeric-rank {
    font-size: 0.8em;
    font-weight: normal;
    opacity: 0.8;
}

.tier-rank {
    display: flex;
    flex-direction: column;
    align-items: center;
    font-weight: bold;
    text-shadow: 0 0 5px currentColor;
}

.tier-icon {
    font-size: 1.5em;
    margin-bottom: 0.2em;
    filter: drop-shadow(0 0 4px currentColor);
    animation: pulse 2s infinite;
}

.tier-name {
    font-size: 0.9em;
    white-space: nowrap;
}

.rank-revealed .rank-container {
    opacity: 1;
    transform: scale(1);
}

/* Tier-specific styling and animations */
[data-tier="thách-đấu"] .stat-card.warning {
    background: linear-gradient(135deg, rgba(255, 78, 255, 0.1) 0%, rgba(183, 0, 165, 0.1) 100%);
    border-color: rgba(255, 78, 255, 0.3);
    animation: challengerGlow 3s infinite;
}

[data-tier="cao-thủ"] .stat-card.warning {
    background: linear-gradient(135deg, rgba(255, 85, 85, 0.1) 0%, rgba(183, 0, 0, 0.1) 100%);
    border-color: rgba(255, 85, 85, 0.3);
}

[data-tier="tinh-anh"] .stat-card.warning {
    background: linear-gradient(135deg, rgba(140, 0, 255, 0.1) 0%, rgba(88, 0, 163, 0.1) 100%);
    border-color: rgba(140, 0, 255, 0.3);
}

[data-tier="kim-cương"] .stat-card.warning {
    background: linear-gradient(135deg, rgba(0, 170, 255, 0.1) 0%, rgba(0, 102, 204, 0.1) 100%);
    border-color: rgba(0, 170, 255, 0.3);
}

@keyframes challengerGlow {
    0% { box-shadow: 0 0 10px rgba(255, 78, 255, 0.5); }
    50% { box-shadow: 0 0 30px rgba(255, 78, 255, 0.8), 0 0 50px rgba(255, 78, 255, 0.4); }
    100% { box-shadow: 0 0 10px rgba(255, 78, 255, 0.5); }
}

[data-tier="thách-đấu"] .tier-icon {
    animation: challenger-shine 3s infinite;
}

[data-tier="cao-thủ"] .tier-icon,
[data-tier="tinh-anh"] .tier-icon,
[data-tier="kim-cương"] .tier-icon {
    animation: shimmer 2s infinite;
}

@keyframes challenger-shine {
    0% { 
        filter: drop-shadow(0 0 5px currentColor); 
        transform: scale(1) rotate(0deg);
    }
    25% { 
        filter: drop-shadow(0 0 10px currentColor);
        transform: scale(1.1) rotate(5deg);
    }
    50% { 
        filter: drop-shadow(0 0 15px currentColor);
        transform: scale(1.15) rotate(0deg);
    }
    75% { 
        filter: drop-shadow(0 0 10px currentColor);
        transform: scale(1.1) rotate(-5deg);
    }
    100% { 
        filter: drop-shadow(0 0 5px currentColor);
        transform: scale(1) rotate(0deg);
    }
}

@keyframes shimmer {
    0% { filter: drop-shadow(0 0 3px currentColor); }
    50% { filter: drop-shadow(0 0 7px currentColor); }
    100% { filter: drop-shadow(0 0 3px currentColor); }
}

/* Particles for special effects */
.particles-container {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    pointer-events: none;
    z-index: 10;
}

.particle {
    position: absolute;
    top: 0;
    left: 0;
    border-radius: 50%;
    pointer-events: none;
}

/* Confetti animations */
@keyframes confettiFall {
    0% { 
        transform: translateY(0) rotate(var(--rotation, 0deg)); 
        opacity: var(--opacity, 0.8);
    }
    70% {
        opacity: var(--opacity, 0.8);
    }
    100% { 
        transform: translateY(100vh) rotate(var(--rotation, 0deg));
        opacity: 0;
    }
}

/* Mobile responsive */
@media (max-width: 768px) {
    .results-container {
        padding: 5rem 1rem 2rem;
    }
    
    .results-header h1 {
        font-size: 2rem;
    }
    
    .sort-buttons {
        gap: 0.5rem;
    }
    
    .sort-btn {
        padding: 0.5rem 1.5rem;
        font-size: 0.9rem;
    }
    
    .question-result {
        padding: 1rem;
    }
    
    .question-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}

@media (max-width: 480px) {
    .sort-btn {
        padding: 0.5rem 1rem;
        font-size: 0.85rem;
    }
    
    .option-item {
        padding: 0.75rem 1rem;
    }
}

/* ===== NAVIGATION STYLES ===== */
.main-nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    padding: 1rem 2rem;
    background: rgba(10, 10, 15, 0.8);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--glass-border);
    z-index: var(--z-sticky);
    transition: var(--transition-normal);
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    font-size: 1.5rem;
    font-weight: 800;
    text-decoration: none;
    transition: var(--transition-fast);
}

.logo-gradient {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-logo:hover {
    transform: scale(1.05);
}

.nav-links {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius-full);
    transition: var(--transition-fast);
    position: relative;
}

.nav-link:hover {
    background: var(--glass-bg);
    color: var(--neon-purple);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(168, 85, 247, 0.3);
}

.nav-link i {
    font-size: 1.1rem;
}

.nav-mobile-toggle {
    display: none;
    color: var(--text-primary);
    font-size: 1.5rem;
    cursor: pointer;
    transition: var(--transition-fast);
}

.nav-mobile-toggle:hover {
    color: var(--neon-purple);
}

/* Navigation Responsive */
@media (max-width: 768px) {
    .nav-links {
        position: fixed;
        top: 70px;
        left: 0;
        right: 0;
        background: var(--bg-secondary);
        backdrop-filter: blur(20px);
        border-bottom: 1px solid var(--glass-border);
        flex-direction: column;
        padding: 1rem;
        gap: 0.5rem;
        transform: translateY(-100%);
        opacity: 0;
        transition: all var(--transition-normal);
        z-index: -1;
    }
    
    .nav-links.active {
        transform: translateY(0);
        opacity: 1;
        z-index: 999;
    }
    
    .nav-link {
        width: 100%;
        justify-content: center;
    }
    
    .nav-mobile-toggle {
        display: block;
    }
}

@media (max-width: 480px) {
    .main-nav {
        padding: 1rem;
    }
    
    .nav-logo {
        font-size: 1.2rem;
    }
    
    .nav-link span {
        display: none;
    }
    
    .nav-link {
        padding: 0.75rem;
    }
    
    .nav-links {
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: center;
    }
}

/* ===== ADDITIONAL MODERN STYLES FOR CONSISTENCY ===== */