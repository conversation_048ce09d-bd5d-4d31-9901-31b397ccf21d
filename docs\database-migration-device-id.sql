-- Database Migration: Add Device ID and Session Management Support
-- This migration adds support for device-based authentication and single session enforcement

-- Add new columns to students table
ALTER TABLE students 
ADD COLUMN IF NOT EXISTS approved_device_id VARCHAR(255),
ADD COLUMN IF NOT EXISTS current_session_id VARCHAR(255),
ADD COLUMN IF NOT EXISTS last_login_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS device_registered_at TIMESTAMP WITH TIME ZONE;

-- Create index for faster device ID lookups
CREATE INDEX IF NOT EXISTS idx_students_approved_device_id ON students(approved_device_id);

-- Create index for session ID lookups
CREATE INDEX IF NOT EXISTS idx_students_current_session_id ON students(current_session_id);

-- Add comments for documentation
COMMENT ON COLUMN students.approved_device_id IS 'Unique device identifier hash for device-based authentication';
COMMENT ON COLUMN students.current_session_id IS 'Current active session ID for single session enforcement';
COMMENT ON COLUMN students.last_login_at IS 'Timestamp of last successful login';
COMMENT ON COLUMN students.device_registered_at IS 'Timestamp when device was first registered';

-- Optional: Create a view for admin interface that shows device status
CREATE OR REPLACE VIEW student_device_status AS
SELECT 
    s.id,
    s.full_name,
    s.phone_number,
    s.is_approved,
    s.approved_device_id,
    s.approved_device_fingerprint, -- Keep for transition period
    s.current_session_id,
    s.last_login_at,
    s.device_registered_at,
    CASE 
        WHEN s.approved_device_id IS NOT NULL THEN 'Device Registered'
        WHEN s.approved_device_fingerprint IS NOT NULL THEN 'Legacy Fingerprint'
        ELSE 'No Device'
    END as device_status,
    CASE 
        WHEN s.current_session_id IS NOT NULL THEN 'Active Session'
        ELSE 'No Active Session'
    END as session_status
FROM students s
WHERE s.is_approved = true;

-- Grant appropriate permissions (adjust based on your setup)
-- GRANT SELECT ON student_device_status TO your_app_user;

-- Migration verification queries
-- Use these to verify the migration was successful:

-- Check if new columns exist
-- SELECT column_name, data_type, is_nullable 
-- FROM information_schema.columns 
-- WHERE table_name = 'students' 
-- AND column_name IN ('approved_device_id', 'current_session_id', 'last_login_at', 'device_registered_at');

-- Check indexes
-- SELECT indexname, indexdef 
-- FROM pg_indexes 
-- WHERE tablename = 'students' 
-- AND indexname LIKE 'idx_students_%device%';

-- Sample data migration (if needed to migrate from fingerprint to device ID)
-- This would be run after the application is updated to generate device IDs
-- UPDATE students 
-- SET approved_device_id = approved_device_fingerprint 
-- WHERE approved_device_fingerprint IS NOT NULL 
-- AND approved_device_id IS NULL;

-- Rollback script (if needed)
-- ALTER TABLE students 
-- DROP COLUMN IF EXISTS approved_device_id,
-- DROP COLUMN IF EXISTS current_session_id,
-- DROP COLUMN IF EXISTS last_login_at,
-- DROP COLUMN IF EXISTS device_registered_at;
-- DROP VIEW IF EXISTS student_device_status;
-- DROP INDEX IF EXISTS idx_students_approved_device_id;
-- DROP INDEX IF EXISTS idx_students_current_session_id;
