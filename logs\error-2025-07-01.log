[2025-07-01T17:27:31.551Z] [ERROR] FATAL: Uncaught Exception | {"error":"app.use() requires a middleware function","stack":"TypeError: app.use() requires a middleware function\n    at Function.use (C:\\Users\\<USER>\\Documents\\OnluyenVatLy\\node_modules\\express\\lib\\application.js:217:11)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\OnluyenVatLy\\api\\index.js:96:5)\n    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n    at Object..js (node:internal/modules/cjs/loader:1699:10)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)\n    at node:internal/main/run_main_module:36:49"}
[2025-07-01T17:27:50.304Z] [ERROR] FATAL: Uncaught Exception | {"error":"app.use() requires a middleware function","stack":"TypeError: app.use() requires a middleware function\n    at Function.use (C:\\Users\\<USER>\\Documents\\OnluyenVatLy\\node_modules\\express\\lib\\application.js:217:11)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\OnluyenVatLy\\api\\index.js:96:5)\n    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n    at Object..js (node:internal/modules/cjs/loader:1699:10)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)\n    at node:internal/main/run_main_module:36:49"}
