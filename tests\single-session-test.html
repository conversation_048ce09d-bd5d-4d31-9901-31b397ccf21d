<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Single Session Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success { background-color: #d4edda; color: #155724; }
        .status.error { background-color: #f8d7da; color: #721c24; }
        .status.info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🔐 Single Session Enforcement Test</h1>
    <p>This test demonstrates that only one session can be active at a time, with new logins taking priority.</p>

    <div class="test-container">
        <h2>Test Setup</h2>
        <div>
            <label>Phone Number: <input type="tel" id="phone" value="0123456789" /></label><br><br>
            <label>Password: <input type="password" id="password" value="test123" /></label><br><br>
            <button onclick="testDeviceIdConsistency()">Test Device ID Consistency</button>
            <button onclick="showDeviceCharacteristics()">Show Device Characteristics</button>
        </div>
    </div>

    <div class="test-container">
        <h2>Session A (First Login)</h2>
        <button onclick="loginSessionA()">Login Session A</button>
        <button onclick="checkSessionA()">Check Session A Status</button>
        <button onclick="logoutSessionA()">Logout Session A</button>
        <div id="statusA" class="status info">Not logged in</div>
    </div>

    <div class="test-container">
        <h2>Session B (Second Login - Should Terminate Session A)</h2>
        <button onclick="loginSessionB()">Login Session B</button>
        <button onclick="checkSessionB()">Check Session B Status</button>
        <button onclick="logoutSessionB()">Logout Session B</button>
        <div id="statusB" class="status info">Not logged in</div>
    </div>

    <div class="test-container">
        <h2>Test Log</h2>
        <button onclick="clearLog()">Clear Log</button>
        <div id="log" class="log">Ready to test...\n</div>
    </div>

    <script src="/js/device-id.js"></script>
    <script>
        let sessionA = null;
        let sessionB = null;
        let deviceId = null;

        // Initialize device ID
        window.addEventListener('load', async () => {
            try {
                deviceId = await window.deviceIdentifier.generateDeviceId();
                log(`Device ID generated: ${deviceId.substring(0, 16)}...`);

                // Debug: Show device characteristics
                const characteristics = window.deviceIdentifier.getCharacteristics();
                log(`Device characteristics: ${JSON.stringify(characteristics, null, 2)}`);
            } catch (error) {
                log(`Error generating device ID: ${error.message}`);
            }
        });

        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').textContent = 'Log cleared...\n';
        }

        async function testDeviceIdConsistency() {
            log('🔍 Testing Device ID consistency...');
            try {
                // Generate device ID multiple times
                const id1 = await window.deviceIdentifier.generateDeviceId();
                const id2 = await window.deviceIdentifier.generateDeviceId();
                const id3 = await window.deviceIdentifier.generateDeviceId();

                if (id1 === id2 && id2 === id3) {
                    log(`✅ Device ID is consistent: ${id1.substring(0, 16)}...`);
                } else {
                    log(`❌ Device ID inconsistency detected!`);
                    log(`ID1: ${id1.substring(0, 16)}...`);
                    log(`ID2: ${id2.substring(0, 16)}...`);
                    log(`ID3: ${id3.substring(0, 16)}...`);
                }
            } catch (error) {
                log(`❌ Error testing device ID: ${error.message}`);
            }
        }

        async function showDeviceCharacteristics() {
            log('📋 Device Characteristics:');
            try {
                await window.deviceIdentifier.generateDeviceId();
                const characteristics = window.deviceIdentifier.getCharacteristics();
                Object.keys(characteristics).forEach(key => {
                    log(`  ${key}: ${characteristics[key]}`);
                });
            } catch (error) {
                log(`❌ Error getting characteristics: ${error.message}`);
            }
        }

        function updateStatus(session, status, isError = false) {
            const statusDiv = document.getElementById(`status${session}`);
            statusDiv.textContent = status;
            statusDiv.className = `status ${isError ? 'error' : 'success'}`;
        }

        async function loginSessionA() {
            log('🔄 Attempting login for Session A...');
            try {
                const response = await fetch('/api/student/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    credentials: 'include',
                    body: JSON.stringify({
                        phone_number: document.getElementById('phone').value,
                        password: document.getElementById('password').value,
                        device_id: deviceId
                    })
                });

                const result = await response.json();
                if (result.success) {
                    sessionA = result.student;
                    updateStatus('A', `✅ Logged in as ${result.student.name}`);
                    log(`✅ Session A login successful: ${result.student.name}`);
                } else {
                    updateStatus('A', `❌ Login failed: ${result.message}`, true);
                    log(`❌ Session A login failed: ${result.message}`);
                }
            } catch (error) {
                updateStatus('A', `❌ Error: ${error.message}`, true);
                log(`❌ Session A error: ${error.message}`);
            }
        }

        async function loginSessionB() {
            log('🔄 Attempting login for Session B (should terminate Session A)...');
            try {
                const response = await fetch('/api/student/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    credentials: 'include',
                    body: JSON.stringify({
                        phone_number: document.getElementById('phone').value,
                        password: document.getElementById('password').value,
                        device_id: deviceId
                    })
                });

                const result = await response.json();
                if (result.success) {
                    sessionB = result.student;
                    updateStatus('B', `✅ Logged in as ${result.student.name}`);
                    log(`✅ Session B login successful: ${result.student.name}`);
                    log(`🔄 Session A should now be terminated...`);
                    
                    // Check Session A status after a brief delay
                    setTimeout(checkSessionA, 1000);
                } else {
                    updateStatus('B', `❌ Login failed: ${result.message}`, true);
                    log(`❌ Session B login failed: ${result.message}`);
                }
            } catch (error) {
                updateStatus('B', `❌ Error: ${error.message}`, true);
                log(`❌ Session B error: ${error.message}`);
            }
        }

        async function checkSessionA() {
            log('🔍 Checking Session A status...');
            try {
                const response = await fetch('/api/check-student-auth', {
                    credentials: 'include'
                });
                const result = await response.json();
                
                if (result.isAuthenticated) {
                    updateStatus('A', `✅ Active: ${result.student.name}`);
                    log(`✅ Session A is active: ${result.student.name}`);
                } else {
                    updateStatus('A', `❌ Session terminated`, true);
                    log(`❌ Session A has been terminated`);
                    sessionA = null;
                }
            } catch (error) {
                updateStatus('A', `❌ Error checking status`, true);
                log(`❌ Error checking Session A: ${error.message}`);
            }
        }

        async function checkSessionB() {
            log('🔍 Checking Session B status...');
            try {
                const response = await fetch('/api/check-student-auth', {
                    credentials: 'include'
                });
                const result = await response.json();
                
                if (result.isAuthenticated) {
                    updateStatus('B', `✅ Active: ${result.student.name}`);
                    log(`✅ Session B is active: ${result.student.name}`);
                } else {
                    updateStatus('B', `❌ Session terminated`, true);
                    log(`❌ Session B has been terminated`);
                    sessionB = null;
                }
            } catch (error) {
                updateStatus('B', `❌ Error checking status`, true);
                log(`❌ Error checking Session B: ${error.message}`);
            }
        }

        async function logoutSessionA() {
            log('🚪 Logging out Session A...');
            try {
                const response = await fetch('/api/student/logout', {
                    method: 'POST',
                    credentials: 'include'
                });
                const result = await response.json();
                
                if (result.success) {
                    updateStatus('A', '🚪 Logged out');
                    log('✅ Session A logged out successfully');
                    sessionA = null;
                } else {
                    log(`❌ Session A logout failed: ${result.message}`);
                }
            } catch (error) {
                log(`❌ Session A logout error: ${error.message}`);
            }
        }

        async function logoutSessionB() {
            log('🚪 Logging out Session B...');
            try {
                const response = await fetch('/api/student/logout', {
                    method: 'POST',
                    credentials: 'include'
                });
                const result = await response.json();
                
                if (result.success) {
                    updateStatus('B', '🚪 Logged out');
                    log('✅ Session B logged out successfully');
                    sessionB = null;
                } else {
                    log(`❌ Session B logout failed: ${result.message}`);
                }
            } catch (error) {
                log(`❌ Session B logout error: ${error.message}`);
            }
        }
    </script>
</body>
</html>
