<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lịch sử hoạt động - Ôn luyện Vật lí</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Styles -->
    <link rel="stylesheet" href="/css/style.css">
    
    <!-- SheetJS for Excel export -->
    <script src="https://unpkg.com/xlsx/dist/xlsx.full.min.js"></script>
    
    <!-- Canvas Animation -->
    <canvas id="network-canvas"></canvas>
</head>
<body>
    <!-- Loading Indicator -->
    <div id="loading-indicator" class="loading-indicator hidden">
        <div class="spinner"></div>
        <p>Đang tải dữ liệu...</p>
    </div>

    <a href="/" class="home-button">
        <img src="https://styles.redditmedia.com/t5_851o4i/styles/profileIcon_0elfudeu2s5b1.jpg?width=256&height=256&frame=1&auto=webp&crop=256:256,smart&s=86be605407a08efe2894a6bacd089074aca51879" alt="Home">
    </a>
    
    <div class="history-container">
        <div class="history-header">
            <h1>
                <i class="fas fa-history"></i> 
                Lịch sử hoạt động
            </h1>
            <div class="export-controls">
                <button onclick="exportToExcel()" class="export-btn">
                    <i class="fas fa-file-export"></i> 
                    <span>Xuất Excel</span>
                </button>
            </div>
        </div>

        <div class="history-stats-grid">
            <div class="stat-card primary mobile-full">
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-value" id="total-students-history">0</div>
                <div class="stat-label">Tổng số học sinh</div>
            </div>
            <div class="stat-card success mobile-full">
                <div class="stat-icon">
                    <i class="fas fa-file-alt"></i>
                </div>
                <div class="stat-value" id="total-submissions">0</div>
                <div class="stat-label">Tổng số bài nộp</div>
            </div>
            <div class="stat-card info">
                <div class="stat-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stat-value" id="avg-score-history">0</div>
                <div class="stat-label">Tỉ lệ đúng trung bình</div>
            </div>
            <div class="stat-card warning">
                <div class="stat-icon">
                    <i class="fas fa-calendar-alt"></i>
                </div>
                <div class="stat-value" id="submissions-today">0</div>
                <div class="stat-label">Bài nộp hôm nay</div>
            </div>
        </div>

        <div class="history-content">
            <div class="history-card">
                <div class="card-header">
                    <h3>
                        <i class="fas fa-list"></i> 
                        <span>Danh sách bài nộp</span>
                    </h3>
                    <div class="header-controls">
                        <div class="search-box">
                            <i class="fas fa-search search-icon"></i>
                            <input type="text" id="history-filter-input" placeholder="Tìm tên học sinh hoặc tên bài" class="modern-input" />
                            <button id="history-clear-filter-btn" class="clear-btn">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <button onclick="deleteAllHistory()" class="delete-all-btn">
                            <i class="fas fa-trash"></i> 
                            <span>Xóa tất cả</span>
                        </button>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="statistics-table mobile-friendly" id="history-log">
                        <thead>
                            <tr>
                                <th class="mobile-hide">
                                    <i class="fas fa-hashtag"></i> STT
                                </th>
                                <th class="sortable" data-sort="name">
                                    <i class="fas fa-user"></i> Tên 
                                    <i class="fas fa-sort"></i>
                                </th>
                                <th class="sortable" data-sort="lesson">
                                    <i class="fas fa-book"></i> Bài 
                                    <i class="fas fa-sort"></i>
                                </th>
                                <th class="mobile-optional sortable" data-sort="time">
                                    <i class="fas fa-clock"></i> Thời gian 
                                    <i class="fas fa-sort"></i>
                                </th>
                                <th class="sortable" data-sort="score">
                                    <i class="fas fa-star"></i> Điểm 
                                    <i class="fas fa-sort"></i>
                                </th>
                                <th>
                                    <i class="fas fa-eye"></i> Chi tiết
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Activity log data will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination controls -->
                <div id="pagination-controls" class="pagination-container"></div>
            </div>
        </div>
    </div>
    
    <!-- Background Animation Script -->
    <script src="/js/network-animation.js"></script>
    <script src="/js/history.js"></script>
</body>
</html>